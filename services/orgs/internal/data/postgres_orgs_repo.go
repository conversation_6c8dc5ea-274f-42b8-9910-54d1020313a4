package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"

	assets "proto/hero/assets/v2"
	pb "proto/hero/orgs/v1"

	"google.golang.org/protobuf/types/known/timestamppb"

	database "common/database"
	"common/utils"
)

// postgresOrgRepository implements OrgRepository using PostgreSQL
type postgresOrgRepository struct {
	db *sql.DB
}

// Helper function to convert a *timestamppb.Timestamp to time.Time.
func TimestamppbToTime(ts *timestamppb.Timestamp) time.Time {
	if ts == nil {
		return time.Time{}
	}
	return ts.AsTime()
}

func (r *postgresOrgRepository) CreateOrg(ctx context.Context, transaction *sql.Tx, org *pb.Org) (*pb.Org, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.Org, error) {
		// Standardize phone numbers if present
		if org.PrimaryPhoneNumber != "" {
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(org.PrimaryPhoneNumber)
			if err != nil {
				return nil, fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		}

		var query string
		var row *sql.Row
		// Allow client to specify org ID, but if not, the DB will auto-assign the next available ID
		if org.Id == 0 { // org ID is not specified
			query = `
				INSERT INTO orgs (name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id, 
					primary_phone_number, created_at, updated_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
				RETURNING id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
					primary_phone_number, created_at, updated_at
			`
			row = tx.QueryRowContext(ctx, query,
				org.Name,
				pq.Array(org.Domains),
				org.TwimlAppSid,
				org.TwilioNumber,
				org.TwilioNumberSid,
				org.ServiceType,
				org.TemplateId,
				org.PrimaryPhoneNumber,
				TimestamppbToTime(org.CreatedAt),
				TimestamppbToTime(org.UpdatedAt),
			)
		} else { // org ID is specified
			query = `
				INSERT INTO orgs (id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
					primary_phone_number, created_at, updated_at)
				VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
				RETURNING id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
					primary_phone_number, created_at, updated_at
			`
			row = tx.QueryRowContext(ctx, query,
				org.Id,
				org.Name,
				pq.Array(org.Domains),
				org.TwimlAppSid,
				org.TwilioNumber,
				org.TwilioNumberSid,
				org.ServiceType,
				org.TemplateId,
				org.PrimaryPhoneNumber,
				TimestamppbToTime(org.CreatedAt),
				TimestamppbToTime(org.UpdatedAt),
			)
		}

		currentTime := time.Now()
		org.CreatedAt = timestamppb.New(currentTime)
		org.UpdatedAt = timestamppb.New(currentTime)

		var domains []string
		var createdAt, updatedAt time.Time
		var primaryPhoneNumber sql.NullString

		err := row.Scan(
			&org.Id,
			&org.Name,
			pq.Array(&domains),
			&org.TwimlAppSid,
			&org.TwilioNumber,
			&org.TwilioNumberSid,
			&org.ServiceType,
			&org.TemplateId,
			&primaryPhoneNumber,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			return nil, err
		}
		org.Domains = domains
		if primaryPhoneNumber.Valid {
			// Ensure the phone number is standardized when reading from DB
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(primaryPhoneNumber.String)
			if err != nil {
				return nil, fmt.Errorf("invalid primary_phone_number format in database: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		}
		org.CreatedAt = timestamppb.New(createdAt)
		org.UpdatedAt = timestamppb.New(updatedAt)
		return org, nil
	})
}

func (r *postgresOrgRepository) GetOrg(ctx context.Context, transaction *sql.Tx, orgID int32) (*pb.Org, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.Org, error) {
		query := `
			SELECT id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
				primary_phone_number, created_at, updated_at
			FROM orgs
			WHERE id = $1
		`
		org := &pb.Org{}
		var domains []string
		var createdAt, updatedAt time.Time
		var primaryPhoneNumber sql.NullString

		err := tx.QueryRowContext(ctx, query, orgID).Scan(
			&org.Id,
			&org.Name,
			pq.Array(&domains),
			&org.TwimlAppSid,
			&org.TwilioNumber,
			&org.TwilioNumberSid,
			&org.ServiceType,
			&org.TemplateId,
			&primaryPhoneNumber,
			&createdAt,
			&updatedAt,
		)
		if err == sql.ErrNoRows {
			return nil, ErrOrgNotFound
		}
		if err != nil {
			return nil, err
		}
		org.Domains = domains
		if primaryPhoneNumber.Valid {
			org.PrimaryPhoneNumber = primaryPhoneNumber.String
		}
		org.CreatedAt = timestamppb.New(createdAt)
		org.UpdatedAt = timestamppb.New(updatedAt)
		return org, nil
	})
}

func (r *postgresOrgRepository) ListOrgs(ctx context.Context, transaction *sql.Tx) ([]*pb.Org, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) ([]*pb.Org, error) {
		query := `
			SELECT id, name, domains, twiml_app_sid, twilio_number, twilio_number_sid, service_type, template_id,
				primary_phone_number, created_at, updated_at
			FROM orgs
		`
		rows, err := tx.QueryContext(ctx, query)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		var orgs []*pb.Org
		for rows.Next() {
			org := &pb.Org{}
			var domains []string
			var createdAt, updatedAt time.Time
			var twilioNumberSid, primaryPhoneNumber sql.NullString

			err := rows.Scan(
				&org.Id,
				&org.Name,
				pq.Array(&domains),
				&org.TwimlAppSid,
				&org.TwilioNumber,
				&twilioNumberSid,
				&org.ServiceType,
				&org.TemplateId,
				&primaryPhoneNumber,
				&createdAt,
				&updatedAt,
			)
			if err != nil {
				return nil, err
			}
			org.Domains = domains
			if twilioNumberSid.Valid {
				org.TwilioNumberSid = twilioNumberSid.String
			}
			if primaryPhoneNumber.Valid {
				org.PrimaryPhoneNumber = primaryPhoneNumber.String
			}
			org.CreatedAt = timestamppb.New(createdAt)
			org.UpdatedAt = timestamppb.New(updatedAt)
			orgs = append(orgs, org)
		}
		if err := rows.Err(); err != nil {
			return nil, err
		}
		return orgs, nil
	})
}

func (r *postgresOrgRepository) DeleteOrg(ctx context.Context, transaction *sql.Tx, orgID int32) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM orgs WHERE id = $1`
		result, err := tx.ExecContext(ctx, query, orgID)
		if err != nil {
			return err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return ErrOrgNotFound
		}
		return nil
	})
}

func (r *postgresOrgRepository) UpdateOrg(ctx context.Context, transaction *sql.Tx, org *pb.Org) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		// Standardize phone numbers if present
		if org.PrimaryPhoneNumber != "" {
			standardizedNumber, err := utils.StandardizeUSPhoneNumber(org.PrimaryPhoneNumber)
			if err != nil {
				return fmt.Errorf("invalid primary_phone_number format: %v", err)
			}
			org.PrimaryPhoneNumber = standardizedNumber
		}

		query := `
			UPDATE orgs 
			SET name = $1, domains = $2, twiml_app_sid = $3, twilio_number = $4, twilio_number_sid = $5, 
				service_type = $6, template_id = $7, primary_phone_number = $8, updated_at = $9
			WHERE id = $10
		`
		currentTime := time.Now()
		org.UpdatedAt = timestamppb.New(currentTime)
		result, err := tx.ExecContext(ctx, query,
			org.Name,
			pq.Array(org.Domains),
			org.TwimlAppSid,
			org.TwilioNumber,
			org.TwilioNumberSid,
			org.ServiceType,
			org.TemplateId,
			org.PrimaryPhoneNumber,
			TimestamppbToTime(org.UpdatedAt),
			org.Id,
		)
		if err != nil {
			return err
		}
		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return ErrOrgNotFound
		}
		return nil
	})
}

func (r *postgresOrgRepository) CreateOrgAPIUser(ctx context.Context, transaction *sql.Tx, orgID int32, username string, encryptedPassword string, hashedPassword string) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		// First verify the org exists
		_, err := r.GetOrg(ctx, tx, orgID)
		if err != nil {
			return err
		}

		createdAt := time.Now()
		updatedAt := time.Now()

		// Insert the new API user
		query := `
			INSERT INTO org_api_users (id, org_id, username, encrypted_password, hashed_password, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7)
		`
		_, err = tx.ExecContext(ctx, query,
			uuid.New().String(),
			orgID,
			username,
			encryptedPassword,
			hashedPassword,
			createdAt,
			updatedAt,
		)
		if err != nil {
			return fmt.Errorf("failed to create API user: %v", err)
		}

		return nil
	})
}

func (r *postgresOrgRepository) GetOrgAPIUserById(ctx context.Context, transaction *sql.Tx, userId string) (*pb.OrgApiUser, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgApiUser, error) {
		query := `
			SELECT id, org_id, username, encrypted_password, hashed_password, created_at, updated_at
			FROM org_api_users
			WHERE id = $1
		`
		var orgID int32
		var id, username, encryptedPassword, hashedPassword string
		var createdAt, updatedAt time.Time
		err := tx.QueryRowContext(ctx, query, userId).Scan(&id, &orgID, &username, &encryptedPassword, &hashedPassword, &createdAt, &updatedAt)
		if err == sql.ErrNoRows {
			return nil, ErrOrgNotFound
		}
		if err != nil {
			return nil, fmt.Errorf("failed to query credentials: %v", err)
		}
		return &pb.OrgApiUser{
			Id:                id,
			OrgId:             orgID,
			Username:          username,
			EncryptedPassword: encryptedPassword,
			HashedPassword:    hashedPassword,
			CreatedAt:         timestamppb.New(createdAt),
			UpdatedAt:         timestamppb.New(updatedAt),
		}, nil
	})
}

func (r *postgresOrgRepository) GetOrgAPIUser(ctx context.Context, transaction *sql.Tx, username string) (*pb.OrgApiUser, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgApiUser, error) {
		query := `
			SELECT id, org_id, encrypted_password, hashed_password, created_at, updated_at
			FROM org_api_users
			WHERE username = $1
		`
		var orgID int32
		var id, encryptedPassword, hashedPassword string
		var createdAt, updatedAt time.Time
		err := tx.QueryRowContext(ctx, query, username).Scan(&id, &orgID, &encryptedPassword, &hashedPassword, &createdAt, &updatedAt)
		if err == sql.ErrNoRows {
			return nil, ErrOrgNotFound
		}
		if err != nil {
			return nil, fmt.Errorf("failed to query credentials: %v", err)
		}
		return &pb.OrgApiUser{
			Id:                id,
			OrgId:             orgID,
			Username:          username,
			EncryptedPassword: encryptedPassword,
			HashedPassword:    hashedPassword,
			CreatedAt:         timestamppb.New(createdAt),
			UpdatedAt:         timestamppb.New(updatedAt),
		}, nil
	})
}

func (r *postgresOrgRepository) GetZelloChannels(ctx context.Context, transaction *sql.Tx) ([]*pb.ZelloChannel, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) ([]*pb.ZelloChannel, error) {
		selectQuery := `
			SELECT id, org_id, zello_channel_id, display_name
			FROM zello_channels
		`
		queryRows, queryError := tx.QueryContext(ctx, selectQuery)
		if queryError != nil {
			return nil, queryError
		}
		defer queryRows.Close()

		var zelloChannels []*pb.ZelloChannel
		for queryRows.Next() {
			var zelloChannel pb.ZelloChannel
			if scanError := queryRows.Scan(&zelloChannel.Id, &zelloChannel.OrgId, &zelloChannel.ZelloChannelId, &zelloChannel.DisplayName); scanError != nil {
				return nil, scanError
			}
			zelloChannels = append(zelloChannels, &zelloChannel)
		}
		if rowsError := queryRows.Err(); rowsError != nil {
			return nil, rowsError
		}
		return zelloChannels, nil
	})
}

func (r *postgresOrgRepository) CreateZelloChannel(ctx context.Context, transaction *sql.Tx, orgID int32, zelloChannel *pb.ZelloChannel) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		zelloChannel.Id = uuid.New().String()
		query := `
			INSERT INTO zello_channels (id, org_id, zello_channel_id, display_name)
			VALUES ($1, $2, $3, $4)
		`
		_, err := tx.ExecContext(ctx, query,
			zelloChannel.Id,
			orgID,
			zelloChannel.ZelloChannelId,
			zelloChannel.DisplayName,
		)
		if err != nil {
			return fmt.Errorf("failed to create Zello channel: %v", err)
		}
		return nil
	})
}

func (r *postgresOrgRepository) DeleteZelloChannel(ctx context.Context, transaction *sql.Tx, orgID int32, zelloChannelIds []string) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM zello_channels WHERE org_id = $1 AND zello_channel_id = ANY($2)`
		_, err := tx.ExecContext(ctx, query, orgID, pq.Array(zelloChannelIds))
		return err
	})
}

// GetOrgAssetsWithCognitoJwtSub returns a list of assets with Cognito JWT sub for an organization.
func (r *postgresOrgRepository) GetOrgAssetsWithCognitoJwtSub(ctx context.Context, transaction *sql.Tx, orgID int32) ([]*AssetInfo, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) ([]*AssetInfo, error) {
		query := `
			SELECT id, cognito_jwt_sub
			FROM assets
			WHERE org_id = $1
		`

		rows, err := tx.QueryContext(ctx, query, orgID)
		if err != nil {
			return nil, fmt.Errorf("failed to query assets: %v", err)
		}
		defer rows.Close()

		var assets []*AssetInfo
		for rows.Next() {
			asset := &AssetInfo{}
			if err := rows.Scan(&asset.ID, &asset.CognitoJwtSub); err != nil {
				return nil, fmt.Errorf("failed to scan asset row: %v", err)
			}
			assets = append(assets, asset)
		}

		if err := rows.Err(); err != nil {
			return nil, fmt.Errorf("error iterating asset rows: %v", err)
		}

		return assets, nil
	})
}

// GetZelloCreds returns the Zello credentials for an asset.
func (r *postgresOrgRepository) GetZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) (*assets.ZelloCreds, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*assets.ZelloCreds, error) {
		query := `
			SELECT org_id, asset_id, username, encrypted_password
			FROM zello_creds
			WHERE asset_id = $1
		`
		var orgID int32
		var assetID string
		var username, encryptedPassword string
		err := tx.QueryRowContext(ctx, query, assetID).Scan(&orgID, &assetID, &username, &encryptedPassword)
		if err == sql.ErrNoRows {
			return nil, nil
		}
		if err != nil {
			return nil, fmt.Errorf("failed to query Zello credentials: %v", err)
		}
		return &assets.ZelloCreds{
			OrgId:             orgID,
			AssetId:           assetID,
			Username:          username,
			EncryptedPassword: encryptedPassword,
		}, nil
	})
}

// DeleteZelloCreds deletes the Zello credentials for an asset.
func (r *postgresOrgRepository) DeleteZelloCreds(ctx context.Context, transaction *sql.Tx, assetID string) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM zello_creds WHERE asset_id = $1`
		_, err := tx.ExecContext(ctx, query, assetID)
		if err != nil {
			return fmt.Errorf("failed to delete Zello credentials: %v", err)
		}
		return nil
	})
}

func (r *postgresOrgRepository) GetTwilioQueueSid(ctx context.Context, transaction *sql.Tx, orgID int32) (string, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (string, error) {
		query := `SELECT twilio_queue_sid FROM twilio_queue_configurations WHERE org_id = $1`
		var twilioQueueSid sql.NullString
		err := tx.QueryRowContext(ctx, query, orgID).Scan(&twilioQueueSid)
		if err == sql.ErrNoRows {
			return "", nil
		}
		if err != nil {
			return "", fmt.Errorf("failed to query Twilio queue configuration: %v", err)
		}
		if !twilioQueueSid.Valid {
			return "", nil
		}
		return twilioQueueSid.String, nil
	})
}

func (r *postgresOrgRepository) DeleteTwilioQueueConfiguration(ctx context.Context, transaction *sql.Tx, orgID int32) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM twilio_queue_configurations WHERE org_id = $1`
		_, err := tx.ExecContext(ctx, query, orgID)
		return err
	})
}

// StoreTwilioQueueConfiguration stores a Twilio queue configuration for an organization.
func (r *postgresOrgRepository) StoreTwilioQueueConfiguration(ctx context.Context, transaction *sql.Tx, orgID int32, friendlyName string, queueSID string, description string) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `INSERT INTO twilio_queue_configurations (friendly_name, twilio_queue_sid, org_id, description) 
		 VALUES ($1, $2, $3, $4)`
		_, err := tx.ExecContext(
			ctx,
			query,
			friendlyName,
			queueSID,
			orgID,
			description,
		)
		if err != nil {
			return fmt.Errorf("failed to store Twilio queue SID: %w", err)
		}
		return nil
	})
}

func (r *postgresOrgRepository) InsertOrgQueue(ctx context.Context, transaction *sql.Tx, req *pb.InsertOrgQueueRequest) (*pb.OrgQueue, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgQueue, error) {
		query := `
			INSERT INTO twilio_queue_configurations (friendly_name, twilio_queue_sid, org_id, description, created_at, updated_at)
			VALUES ($1, $2, $3, $4, NOW(), NOW())
			RETURNING id, friendly_name, twilio_queue_sid, org_id, description, created_at, updated_at
		`
		row := tx.QueryRowContext(ctx, query, req.FriendlyName, req.TwilioQueueSid, req.OrgId, req.Description)

		orgQueue := &pb.OrgQueue{}
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&orgQueue.Id, // Assuming pb.OrgQueue has an int32 Id field matching the table
			&orgQueue.FriendlyName,
			&orgQueue.TwilioQueueSid,
			&orgQueue.OrgId,
			&orgQueue.Description,
			&createdAt,
			&updatedAt,
		)

		if err != nil {
			if err == sql.ErrNoRows {
				// This shouldn't happen with INSERT ... RETURNING unless something is very wrong
				return nil, fmt.Errorf("failed to insert org queue, no rows returned: %w", err)
			}
			return nil, fmt.Errorf("failed to insert org queue and scan result: %w", err)
		}

		orgQueue.CreatedAt = timestamppb.New(createdAt)
		orgQueue.UpdatedAt = timestamppb.New(updatedAt)

		return orgQueue, nil
	})
}

// AddToContactBook creates a new contact record in the organization's contact book
func (r *postgresOrgRepository) AddToContactBook(ctx context.Context, transaction *sql.Tx, contact *pb.ContactRecord) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `
			INSERT INTO org_contacts_book (id, org_id, name, phone, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6)
		`
		_, err := tx.ExecContext(ctx, query,
			contact.Id,
			contact.OrgId,
			contact.Name,
			contact.Phone,
			TimestamppbToTime(contact.CreatedAt),
			TimestamppbToTime(contact.UpdatedAt),
		)
		return err
	})
}

// UpdateContactInContactBook updates an existing contact record in the organization's contact book
func (r *postgresOrgRepository) UpdateContactInContactBook(ctx context.Context, transaction *sql.Tx, contact *pb.ContactRecord) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `
			UPDATE org_contacts_book 
			SET name = $2, phone = $3, updated_at = $4
			WHERE id = $1
		`
		result, err := tx.ExecContext(ctx, query,
			contact.Id,
			contact.Name,
			contact.Phone,
			TimestamppbToTime(contact.UpdatedAt),
		)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return fmt.Errorf("contact with ID %s not found", contact.Id)
		}

		return nil
	})
}

// DeleteFromContactBook deletes a contact record from the organization's contact book
func (r *postgresOrgRepository) DeleteFromContactBook(ctx context.Context, transaction *sql.Tx, contactID string) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM org_contacts_book WHERE id = $1`
		result, err := tx.ExecContext(ctx, query, contactID)
		if err != nil {
			return err
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return err
		}
		if rowsAffected == 0 {
			return fmt.Errorf("contact with ID %s not found", contactID)
		}

		return nil
	})
}

// GetContactFromContactBook retrieves a contact record by its ID from the organization's contact book
func (r *postgresOrgRepository) GetContactFromContactBook(ctx context.Context, transaction *sql.Tx, contactID string) (*pb.ContactRecord, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.ContactRecord, error) {
		query := `
			SELECT id, org_id, name, phone, created_at, updated_at
			FROM org_contacts_book 
			WHERE id = $1
		`
		row := tx.QueryRowContext(ctx, query, contactID)

		var contact pb.ContactRecord
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&contact.Id,
			&contact.OrgId,
			&contact.Name,
			&contact.Phone,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, fmt.Errorf("contact with ID %s not found", contactID)
			}
			return nil, err
		}

		contact.CreatedAt = timestamppb.New(createdAt)
		contact.UpdatedAt = timestamppb.New(updatedAt)

		return &contact, nil
	})
}

// ContactBookPageResult holds the result of a paginated contact book query
type ContactBookPageResult struct {
	Contacts      []*pb.ContactRecord
	NextPageToken string
	TotalCount    int32
}

// ListContactsInContactBook returns paginated contact records for an organization's contact book
func (r *postgresOrgRepository) ListContactsInContactBook(ctx context.Context, transaction *sql.Tx, orgID int32, pageToken string, pageSize int32) ([]*pb.ContactRecord, string, int32, error) {
	result, err := database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*ContactBookPageResult, error) {
		// Enforce max page size of 100
		if pageSize <= 0 || pageSize > 100 {
			pageSize = 100
		}

		// Handle pagination with OFFSET/LIMIT approach
		// For production, consider using cursor-based pagination for better performance
		var offset int32 = 0
		if pageToken != "" {
			// Simple implementation: pageToken is the offset as string
			// In production, consider encoding more information or using cursor-based pagination
			if parsedOffset, err := strconv.ParseInt(pageToken, 10, 32); err == nil {
				offset = int32(parsedOffset)
			}
		}

		// Get total count
		countQuery := `SELECT COUNT(*) FROM org_contacts_book WHERE org_id = $1`
		var totalCount int32
		err := tx.QueryRowContext(ctx, countQuery, orgID).Scan(&totalCount)
		if err != nil {
			return nil, err
		}

		// Get paginated results
		query := `
			SELECT id, org_id, name, phone, created_at, updated_at
			FROM org_contacts_book 
			WHERE org_id = $1
			ORDER BY name ASC
			LIMIT $2 OFFSET $3
		`
		rows, err := tx.QueryContext(ctx, query, orgID, pageSize, offset)
		if err != nil {
			return nil, err
		}
		defer rows.Close()

		var contacts []*pb.ContactRecord
		for rows.Next() {
			var contact pb.ContactRecord
			var createdAt, updatedAt time.Time

			err := rows.Scan(
				&contact.Id,
				&contact.OrgId,
				&contact.Name,
				&contact.Phone,
				&createdAt,
				&updatedAt,
			)
			if err != nil {
				return nil, err
			}

			contact.CreatedAt = timestamppb.New(createdAt)
			contact.UpdatedAt = timestamppb.New(updatedAt)

			contacts = append(contacts, &contact)
		}

		if err := rows.Err(); err != nil {
			return nil, err
		}

		// Calculate next page token
		var nextPageToken string
		if len(contacts) == int(pageSize) && offset+pageSize < totalCount {
			nextPageToken = strconv.FormatInt(int64(offset+pageSize), 10)
		}

		return &ContactBookPageResult{
			Contacts:      contacts,
			NextPageToken: nextPageToken,
			TotalCount:    totalCount,
		}, nil
	})

	if err != nil {
		return nil, "", 0, err
	}

	return result.Contacts, result.NextPageToken, result.TotalCount, nil
}

// CreateOrgPhoneLine creates a new phone line for an organization
func (r *postgresOrgRepository) CreateOrgPhoneLine(ctx context.Context, transaction *sql.Tx, phoneLine *pb.OrgPhoneLine) (*pb.OrgPhoneLine, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgPhoneLine, error) {
		// Generate ID if not provided
		if phoneLine.Id == 0 {
			phoneLine.Id = int32(uuid.New().ID())
		}

		// Set timestamps
		currentTime := time.Now()
		phoneLine.CreatedAt = timestamppb.New(currentTime)
		phoneLine.UpdatedAt = timestamppb.New(currentTime)

		// If this is being set as primary, unset any existing primary for this org
		if phoneLine.IsPrimaryLine {
			_, err := tx.ExecContext(ctx, `
				UPDATE org_phone_lines 
				SET is_primary_line = FALSE 
				WHERE org_id = $1 AND is_primary_line = TRUE
			`, phoneLine.OrgId)
			if err != nil {
				return nil, fmt.Errorf("failed to unset existing primary line: %v", err)
			}
		}

		query := `
			INSERT INTO org_phone_lines (
				id, org_id, line_type, external_forwarding_number, 
				twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, 
				created_at, updated_at
			) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
			RETURNING id, org_id, line_type, external_forwarding_number, 
				twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, 
				created_at, updated_at
		`

		row := tx.QueryRowContext(ctx, query,
			phoneLine.Id,
			phoneLine.OrgId,
			phoneLine.LineType,
			phoneLine.ExternalForwardingNumber,
			phoneLine.TwilioReceivingNumber,
			phoneLine.TwilioNumberSid,
			phoneLine.TwimlAppSid,
			phoneLine.IsPrimaryLine,
			TimestamppbToTime(phoneLine.CreatedAt),
			TimestamppbToTime(phoneLine.UpdatedAt),
		)

		var externalForwardingNumber, twilioNumberSid, twimlAppSid sql.NullString
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&phoneLine.Id,
			&phoneLine.OrgId,
			&phoneLine.LineType,
			&externalForwardingNumber,
			&phoneLine.TwilioReceivingNumber,
			&twilioNumberSid,
			&twimlAppSid,
			&phoneLine.IsPrimaryLine,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create phone line: %v", err)
		}

		// Handle nullable fields
		if externalForwardingNumber.Valid {
			phoneLine.ExternalForwardingNumber = externalForwardingNumber.String
		}
		if twilioNumberSid.Valid {
			phoneLine.TwilioNumberSid = twilioNumberSid.String
		}
		if twimlAppSid.Valid {
			phoneLine.TwimlAppSid = twimlAppSid.String
		}

		phoneLine.CreatedAt = timestamppb.New(createdAt)
		phoneLine.UpdatedAt = timestamppb.New(updatedAt)

		return phoneLine, nil
	})
}

// GetOrgPhoneLineByTwilioNumber retrieves a phone line by Twilio receiving number
func (r *postgresOrgRepository) GetOrgPhoneLineByTwilioNumber(ctx context.Context, transaction *sql.Tx, twilioReceivingNumber string) (*pb.OrgPhoneLine, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgPhoneLine, error) {
		query := `
			SELECT id, org_id, line_type, external_forwarding_number, 
				twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, 
				created_at, updated_at
			FROM org_phone_lines
			WHERE twilio_receiving_number = $1
		`

		phoneLine := &pb.OrgPhoneLine{}
		var externalForwardingNumber, twilioNumberSid, twimlAppSid sql.NullString
		var createdAt, updatedAt time.Time

		err := tx.QueryRowContext(ctx, query, twilioReceivingNumber).Scan(
			&phoneLine.Id,
			&phoneLine.OrgId,
			&phoneLine.LineType,
			&externalForwardingNumber,
			&phoneLine.TwilioReceivingNumber,
			&twilioNumberSid,
			&twimlAppSid,
			&phoneLine.IsPrimaryLine,
			&createdAt,
			&updatedAt,
		)
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("phone line with Twilio number %s not found", twilioReceivingNumber)
		}
		if err != nil {
			return nil, fmt.Errorf("failed to get phone line: %v", err)
		}

		// Handle nullable fields
		if externalForwardingNumber.Valid {
			phoneLine.ExternalForwardingNumber = externalForwardingNumber.String
		}
		if twilioNumberSid.Valid {
			phoneLine.TwilioNumberSid = twilioNumberSid.String
		}
		if twimlAppSid.Valid {
			phoneLine.TwimlAppSid = twimlAppSid.String
		}

		phoneLine.CreatedAt = timestamppb.New(createdAt)
		phoneLine.UpdatedAt = timestamppb.New(updatedAt)

		return phoneLine, nil
	})
}

// GetOrgPhoneLineByID retrieves a phone line by ID
func (r *postgresOrgRepository) GetOrgPhoneLineByID(ctx context.Context, transaction *sql.Tx, phoneLineID int32) (*pb.OrgPhoneLine, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgPhoneLine, error) {
		query := `
			SELECT id, org_id, line_type, external_forwarding_number, 
				twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, 
				created_at, updated_at
			FROM org_phone_lines
			WHERE id = $1
		`

		phoneLine := &pb.OrgPhoneLine{}
		var externalForwardingNumber, twilioNumberSid, twimlAppSid sql.NullString
		var createdAt, updatedAt time.Time

		err := tx.QueryRowContext(ctx, query, phoneLineID).Scan(
			&phoneLine.Id,
			&phoneLine.OrgId,
			&phoneLine.LineType,
			&externalForwardingNumber,
			&phoneLine.TwilioReceivingNumber,
			&twilioNumberSid,
			&twimlAppSid,
			&phoneLine.IsPrimaryLine,
			&createdAt,
			&updatedAt,
		)
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("phone line with ID %d not found", phoneLineID)
		}
		if err != nil {
			return nil, fmt.Errorf("failed to get phone line: %v", err)
		}

		// Handle nullable fields
		if externalForwardingNumber.Valid {
			phoneLine.ExternalForwardingNumber = externalForwardingNumber.String
		}
		if twilioNumberSid.Valid {
			phoneLine.TwilioNumberSid = twilioNumberSid.String
		}
		if twimlAppSid.Valid {
			phoneLine.TwimlAppSid = twimlAppSid.String
		}

		phoneLine.CreatedAt = timestamppb.New(createdAt)
		phoneLine.UpdatedAt = timestamppb.New(updatedAt)

		return phoneLine, nil
	})
}

// ListOrgPhoneLinesByOrgID retrieves all phone lines for an organization
func (r *postgresOrgRepository) ListOrgPhoneLinesByOrgID(ctx context.Context, transaction *sql.Tx, orgID int32) ([]*pb.OrgPhoneLine, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) ([]*pb.OrgPhoneLine, error) {
		query := `
			SELECT id, org_id, line_type, external_forwarding_number, 
				twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, 
				created_at, updated_at
			FROM org_phone_lines
			WHERE org_id = $1
			ORDER BY is_primary_line DESC, line_type ASC, created_at ASC
		`

		rows, err := tx.QueryContext(ctx, query, orgID)
		if err != nil {
			return nil, fmt.Errorf("failed to query phone lines: %v", err)
		}
		defer rows.Close()

		var phoneLines []*pb.OrgPhoneLine
		for rows.Next() {
			phoneLine := &pb.OrgPhoneLine{}
			var externalForwardingNumber, twilioNumberSid, twimlAppSid sql.NullString
			var createdAt, updatedAt time.Time

			err := rows.Scan(
				&phoneLine.Id,
				&phoneLine.OrgId,
				&phoneLine.LineType,
				&externalForwardingNumber,
				&phoneLine.TwilioReceivingNumber,
				&twilioNumberSid,
				&twimlAppSid,
				&phoneLine.IsPrimaryLine,
				&createdAt,
				&updatedAt,
			)
			if err != nil {
				return nil, fmt.Errorf("failed to scan phone line: %v", err)
			}

			// Handle nullable fields
			if externalForwardingNumber.Valid {
				phoneLine.ExternalForwardingNumber = externalForwardingNumber.String
			}
			if twilioNumberSid.Valid {
				phoneLine.TwilioNumberSid = twilioNumberSid.String
			}
			if twimlAppSid.Valid {
				phoneLine.TwimlAppSid = twimlAppSid.String
			}

			phoneLine.CreatedAt = timestamppb.New(createdAt)
			phoneLine.UpdatedAt = timestamppb.New(updatedAt)

			phoneLines = append(phoneLines, phoneLine)
		}

		if err := rows.Err(); err != nil {
			return nil, fmt.Errorf("error iterating phone lines: %v", err)
		}

		return phoneLines, nil
	})
}

// UpdateOrgPhoneLine updates an existing phone line
func (r *postgresOrgRepository) UpdateOrgPhoneLine(ctx context.Context, transaction *sql.Tx, phoneLine *pb.OrgPhoneLine) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		// Update timestamp
		phoneLine.UpdatedAt = timestamppb.New(time.Now())

		// If this is being set as primary, unset any existing primary for this org
		if phoneLine.IsPrimaryLine {
			_, err := tx.ExecContext(ctx, `
				UPDATE org_phone_lines 
				SET is_primary_line = FALSE 
				WHERE org_id = $1 AND id != $2 AND is_primary_line = TRUE
			`, phoneLine.OrgId, phoneLine.Id)
			if err != nil {
				return fmt.Errorf("failed to unset existing primary line: %v", err)
			}
		}

		query := `
			UPDATE org_phone_lines 
			SET line_type = $2, external_forwarding_number = $3, 
				twilio_receiving_number = $4, twilio_number_sid = $5, 
				twiml_app_sid = $6, is_primary_line = $7, updated_at = $8
			WHERE id = $1
		`

		result, err := tx.ExecContext(ctx, query,
			phoneLine.Id,
			phoneLine.LineType,
			phoneLine.ExternalForwardingNumber,
			phoneLine.TwilioReceivingNumber,
			phoneLine.TwilioNumberSid,
			phoneLine.TwimlAppSid,
			phoneLine.IsPrimaryLine,
			TimestamppbToTime(phoneLine.UpdatedAt),
		)
		if err != nil {
			return fmt.Errorf("failed to update phone line: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %v", err)
		}
		if rowsAffected == 0 {
			return fmt.Errorf("phone line with ID %d not found", phoneLine.Id)
		}

		return nil
	})
}

// DeleteOrgPhoneLine deletes a phone line
func (r *postgresOrgRepository) DeleteOrgPhoneLine(ctx context.Context, transaction *sql.Tx, phoneLineID int32) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		query := `DELETE FROM org_phone_lines WHERE id = $1`
		result, err := tx.ExecContext(ctx, query, phoneLineID)
		if err != nil {
			return fmt.Errorf("failed to delete phone line: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %v", err)
		}
		if rowsAffected == 0 {
			return fmt.Errorf("phone line with ID %d not found", phoneLineID)
		}

		return nil
	})
}

// SetPrimaryOrgPhoneLine sets a phone line as the primary line for an organization
func (r *postgresOrgRepository) SetPrimaryOrgPhoneLine(ctx context.Context, transaction *sql.Tx, orgID int32, phoneLineID int32) error {
	return database.WithSessionErr(r.db, ctx, transaction, func(tx *sql.Tx) error {
		// First, unset any existing primary line for this org
		_, err := tx.ExecContext(ctx, `
			UPDATE org_phone_lines 
			SET is_primary_line = FALSE 
			WHERE org_id = $1 AND is_primary_line = TRUE
		`, orgID)
		if err != nil {
			return fmt.Errorf("failed to unset existing primary line: %v", err)
		}

		// Then set the specified line as primary
		result, err := tx.ExecContext(ctx, `
			UPDATE org_phone_lines 
			SET is_primary_line = TRUE, updated_at = NOW()
			WHERE id = $1 AND org_id = $2
		`, phoneLineID, orgID)
		if err != nil {
			return fmt.Errorf("failed to set primary line: %v", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %v", err)
		}
		if rowsAffected == 0 {
			return fmt.Errorf("phone line with ID %d not found for org %d", phoneLineID, orgID)
		}

		return nil
	})
}

// GetPrimaryOrgPhoneLine retrieves the primary phone line for an organization
func (r *postgresOrgRepository) GetPrimaryOrgPhoneLine(ctx context.Context, transaction *sql.Tx, orgID int32) (*pb.OrgPhoneLine, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.OrgPhoneLine, error) {
		query := `
			SELECT id, org_id, line_type, external_forwarding_number, 
				twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line, 
				created_at, updated_at
			FROM org_phone_lines
			WHERE org_id = $1 AND is_primary_line = TRUE
		`

		phoneLine := &pb.OrgPhoneLine{}
		var externalForwardingNumber, twilioNumberSid, twimlAppSid sql.NullString
		var createdAt, updatedAt time.Time

		err := tx.QueryRowContext(ctx, query, orgID).Scan(
			&phoneLine.Id,
			&phoneLine.OrgId,
			&phoneLine.LineType,
			&externalForwardingNumber,
			&phoneLine.TwilioReceivingNumber,
			&twilioNumberSid,
			&twimlAppSid,
			&phoneLine.IsPrimaryLine,
			&createdAt,
			&updatedAt,
		)
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no primary phone line found for org %d", orgID)
		}
		if err != nil {
			return nil, fmt.Errorf("failed to get primary phone line: %v", err)
		}

		// Handle nullable fields
		if externalForwardingNumber.Valid {
			phoneLine.ExternalForwardingNumber = externalForwardingNumber.String
		}
		if twilioNumberSid.Valid {
			phoneLine.TwilioNumberSid = twilioNumberSid.String
		}
		if twimlAppSid.Valid {
			phoneLine.TwimlAppSid = twimlAppSid.String
		}

		phoneLine.CreatedAt = timestamppb.New(createdAt)
		phoneLine.UpdatedAt = timestamppb.New(updatedAt)

		return phoneLine, nil
	})
}

// GetContactByPhoneNumber retrieves a contact record by phone number from the organization's contact book
func (r *postgresOrgRepository) GetContactByPhoneNumber(ctx context.Context, transaction *sql.Tx, orgID int32, phone string) (*pb.ContactRecord, error) {
	return database.WithSession(r.db, ctx, transaction, func(tx *sql.Tx) (*pb.ContactRecord, error) {
		query := `
			SELECT id, org_id, name, phone, created_at, updated_at
			FROM org_contacts_book	 
			WHERE org_id = $1 AND phone = $2
			LIMIT 1
		`
		row := tx.QueryRowContext(ctx, query, orgID, phone)

		var contact pb.ContactRecord
		var createdAt, updatedAt time.Time

		err := row.Scan(
			&contact.Id,
			&contact.OrgId,
			&contact.Name,
			&contact.Phone,
			&createdAt,
			&updatedAt,
		)
		if err != nil {
			if err == sql.ErrNoRows {
				return nil, fmt.Errorf("contact with phone %s not found in org %d", phone, orgID)
			}
			return nil, err
		}

		contact.CreatedAt = timestamppb.New(createdAt)
		contact.UpdatedAt = timestamppb.New(updatedAt)

		return &contact, nil
	})
}
