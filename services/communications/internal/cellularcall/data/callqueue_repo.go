package data

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"os"
	"time"

	"common/database"

	_ "github.com/lib/pq"
)

// Common errors that can be returned by repository operations
var (
	ErrCallNotFound    = errors.New("call not found")
	ErrInvalidInput    = errors.New("invalid input parameter")
	ErrNoActiveCall    = errors.New("no active call for agent")
	ErrCallStateChange = errors.New("invalid call state transition")
)

// QueuedCall represents a call in the system (waiting, active, or on hold)
type QueuedCall struct {
	CallSID       string            // Unique call identifier from Twilio
	Caller        string            // Caller's phone number
	CallerName    string            // Optional caller name if available
	EnqueueTime   time.Time         // When the call was added to the queue
	AssetID       string            // Current owner (empty if in waiting queue)
	SituationID   string            // Associated situation (automatically created)
	State         string            // "waiting", "active", "hold"
	Direction     string            // "inbound" or "outbound"
	Attributes    map[string]string // Generic attributes for extension
	CallStartTime *time.Time        `db:"call_start_time"` // When the call was connected
	CallEndTime   *time.Time        `db:"call_end_time"`   // When the call ended (null for active calls)
	LastHoldStart *time.Time        `db:"last_hold_start"` // When the call was last placed on hold (null if not on hold)
	// Multi-line support fields
	PhoneLineId           int32  // ID of the phone line used for this call
	TwilioReceivingNumber string // The Twilio number that received the call
	LineType              string // Type of phone line (general, emergency, parking)
}

// GetCallDuration returns the duration of the call so far or total if ended.
// Returns 0 if the call hasn't started yet.
func (c *QueuedCall) GetCallDuration() time.Duration {
	if c.CallStartTime == nil {
		return 0
	}

	// If call has ended, calculate from start to end
	if c.CallEndTime != nil {
		return c.CallEndTime.Sub(*c.CallStartTime)
	}

	// Otherwise calculate from start to now (ongoing call)
	return time.Since(*c.CallStartTime)
}

// IsOnHold returns true if the call is currently on hold
func (c *QueuedCall) IsOnHold() bool {
	return c.State == CallStateHold && c.LastHoldStart != nil
}

// GetCurrentHoldDuration returns the duration of the current hold if the call is on hold.
// Returns 0 if the call is not on hold.
func (c *QueuedCall) GetCurrentHoldDuration() time.Duration {
	if !c.IsOnHold() {
		return 0
	}

	return time.Since(*c.LastHoldStart)
}

// IsActive returns true if the call is active (not waiting, not ended)
func (c *QueuedCall) IsActive() bool {
	return c.CallStartTime != nil && c.CallEndTime == nil
}

// CallDirection constants define the possible directions of a call
const (
	CallDirectionInbound  = "inbound"  // Call was received from an external party
	CallDirectionOutbound = "outbound" // Call was initiated by an agent
)

// CallState constants define the possible states of a call
const (
	CallStateWaiting                = "waiting"                      // Call is in the queue waiting to be answered
	CallStateActive                 = "active"                       // Call is currently being handled by an agent
	CallStatePendingSelectiveAssign = "pending_selective_assignment" // Call has been claimed by specific agent but not yet connected
	CallStateHold                   = "hold"                         // Call has been placed on hold by an agent
	CallStateEnded                  = "ended"                        // Call has ended (for historical tracking)
)

// QueueStrategy defines how to select the next call from the queue
type QueueStrategy interface {
	SelectNextCall(ctx context.Context, calls []QueuedCall) (QueuedCall, int, bool, error)
}

type OrgTwilioDetails struct {
	TwimlAppSid     string
	TwilioNumber    string
	TwilioApiUserId string
	OrgName         string
	// Multi-line support fields
	PhoneLineId   int32  // ID of the specific phone line
	LineType      string // Type of phone line (general, emergency, parking)
	IsPrimaryLine bool   // Whether this is the primary line
}

// OrgPhoneLineDetails represents a specific phone line for an organization
type OrgPhoneLineDetails struct {
	Id                       int32
	OrgId                    int32
	LineType                 string
	ExternalForwardingNumber string
	TwilioReceivingNumber    string
	TwilioNumberSid          string
	TwimlAppSid              string
	IsPrimaryLine            bool
	OrgName                  string
	TwilioApiUserId          string
}

// CallQueueRepository defines the interface for call queue operations
type CallQueueRepository interface {
	GetOrgTwilioDetails(ctx context.Context) (OrgTwilioDetails, error)
	// Multi-line support methods
	GetOrgTwilioDetailsByPhoneNumber(ctx context.Context, phoneNumber string) (OrgPhoneLineDetails, error)
	GetOrgPhoneLineDetails(ctx context.Context, orgId int32, lineType string) (OrgPhoneLineDetails, error)
	ListOrgPhoneLines(ctx context.Context, orgId int32) ([]OrgPhoneLineDetails, error)
	// Queue Management
	EnqueueCall(ctx context.Context, call QueuedCall) error
	DequeueCall(ctx context.Context, agentID string) (QueuedCall, bool, error)
	DequeueCallBySid(ctx context.Context, callSID string, agentID string) (QueuedCall, bool, error)

	// Call Status Management
	HoldCall(ctx context.Context, callSID string, agentID string) error
	ResumeCall(ctx context.Context, callSID string, agentID string) error
	EndCall(ctx context.Context, callSID string) error
	RevertSelectiveClaim(ctx context.Context, callSID string) (bool, error)

	// Queries
	GetActiveCall(ctx context.Context, agentID string) (QueuedCall, bool, error)
	GetHeldCalls(ctx context.Context, agentID string) ([]QueuedCall, error)
	GetCallByCallSID(ctx context.Context, callSID string) (QueuedCall, bool, error)
	GetCallBySituationID(ctx context.Context, situationID string) (QueuedCall, bool, error)
	GetQueueStatus(ctx context.Context) (int, []QueuedCall, error)
	GetTwilioQueueSid(ctx context.Context) (string, error)
	GetTwilioQueueName(ctx context.Context) (string, error)
	StoreActiveCall(ctx context.Context, call QueuedCall) error

	// Situation Assignment
	SetCallSituation(ctx context.Context, callSID string, situationID string) error

	// Strategy Management (optional extension point)
	SetQueueStrategy(strategy QueueStrategy)
}

// NewCallQueueRepository initializes the call queue repository based on the REPO_TYPE environment variable.
func NewCallQueueRepository() (CallQueueRepository, error) {
	// Existing implementation...
	// Return the appropriate repository type
	repoType := os.Getenv("REPO_TYPE")
	if repoType == "" {
		repoType = "memory" // Default to in-memory for MVP
	}
	log.Printf("Initializing Call Queue Repository with type: %s", repoType)

	switch repoType {
	case "postgres":
		databaseURL, err := database.CreateDBURL()
		if err != nil {
			log.Fatalf("failed to get postgres db url: %v", err)
		}
		postGresDB, openError := sql.Open("postgres", databaseURL)
		if openError != nil {
			log.Fatalf("failed to open postgres db: %v", openError)
		}

		log.Println("Successfully connected to PostgreSQL for call queue")
		return NewPostgresCallQueueRepository(postGresDB), nil

	case "memory":
		log.Println("Using in-memory call queue repository")
		return NewInMemoryCallQueueRepository(), nil

	default:
		return nil, fmt.Errorf("invalid REPO_TYPE: %s", repoType)
	}
}
