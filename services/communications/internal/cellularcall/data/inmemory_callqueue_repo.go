package data

import (
	"context"
	"log"
	"os"
	"sync"
	"time"
)

// inMemoryCallQueueRepository is an in-memory implementation of CallQueueRepository.
type inMemoryCallQueueRepository struct {
	mu            sync.RWMutex
	waitingCalls  []QueuedCall          // FIFO queue for incoming calls
	onHoldCalls   []QueuedCall          // List of calls placed on hold
	activeCalls   map[string]QueuedCall // Map of assetID to active call
	queueStrategy QueueStrategy         // Strategy for selecting next call
}

// Update the constructor
func NewInMemoryCallQueueRepository() CallQueueRepository {

	return &inMemoryCallQueueRepository{
		waitingCalls:  make([]QueuedCall, 0),
		onHoldCalls:   make([]QueuedCall, 0),
		activeCalls:   make(map[string]QueuedCall),
		queueStrategy: nil, // Will be set by usecase
	}
}

// EnqueueCall adds a call to the waiting queue
func (r *inMemoryCallQueueRepository) EnqueueCall(ctx context.Context, call QueuedCall) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Check for context cancellation
	if ctx.Err() != nil {
		return ctx.Err()
	}

	// Validate inputs
	if call.CallSID == "" {
		return ErrInvalidInput
	}

	// Set enqueue time if not already set
	if call.EnqueueTime.IsZero() {
		call.EnqueueTime = time.Now()
	}

	// Set default direction to inbound if not provided
	if call.Direction == "" {
		call.Direction = CallDirectionInbound
	}

	// Set state to waiting
	call.State = CallStateWaiting

	// Add to queue
	r.waitingCalls = append(r.waitingCalls, call)
	return nil
}

// DequeueCall gets the next call and assigns it to an asset
func (r *inMemoryCallQueueRepository) DequeueCall(ctx context.Context, assetID string) (QueuedCall, bool, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	// Check for context cancellation
	if ctx.Err() != nil {
		return QueuedCall{}, false, ctx.Err()
	}

	if assetID == "" {
		return QueuedCall{}, false, ErrInvalidInput
	}

	// Check if there are calls in the queue
	if len(r.waitingCalls) == 0 {
		return QueuedCall{}, false, nil
	}

	// Use strategy to select next call if available
	var call QueuedCall
	var index int
	var ok bool
	var err error

	if r.queueStrategy != nil {
		call, index, ok, err = r.queueStrategy.SelectNextCall(ctx, r.waitingCalls)
		if err != nil {
			return QueuedCall{}, false, err
		}
		if !ok {
			return QueuedCall{}, false, nil
		}
	} else {
		// Default to first call if no strategy
		call = r.waitingCalls[0]
		index = 0
	}

	// Remove from waiting queue
	r.waitingCalls = append(r.waitingCalls[:index], r.waitingCalls[index+1:]...)

	// Assign to asset and set as active
	call.AssetID = assetID
	call.State = CallStateActive
	now := time.Now()
	call.CallStartTime = &now

	// Store as active call
	r.activeCalls[assetID] = call

	return call, true, nil
}

// HoldCall places an active call on hold
func (r *inMemoryCallQueueRepository) HoldCall(ctx context.Context, callSID string, assetID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if ctx.Err() != nil {
		return ctx.Err()
	}

	if callSID == "" || assetID == "" {
		return ErrInvalidInput
	}

	// Check if this is the asset's active call
	activeCall, exists := r.activeCalls[assetID]
	if !exists || activeCall.CallSID != callSID {
		return ErrNoActiveCall
	}

	// Change state to hold
	activeCall.State = CallStateHold
	now := time.Now()
	activeCall.LastHoldStart = &now

	// Add to hold list
	r.onHoldCalls = append(r.onHoldCalls, activeCall)

	// Remove from active calls
	delete(r.activeCalls, assetID)

	return nil
}

// ResumeCall takes a call off hold
func (r *inMemoryCallQueueRepository) ResumeCall(ctx context.Context, callSID string, assetID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if ctx.Err() != nil {
		return ctx.Err()
	}

	if callSID == "" || assetID == "" {
		return ErrInvalidInput
	}

	// Find the call on hold
	for i := range r.onHoldCalls {
		if r.onHoldCalls[i].CallSID == callSID && r.onHoldCalls[i].AssetID == assetID {
			// Get the call before removing it
			call := r.onHoldCalls[i]

			// Remove from hold list
			r.onHoldCalls = append(r.onHoldCalls[:i], r.onHoldCalls[i+1:]...)

			// Change state to active
			call.State = CallStateActive
			call.LastHoldStart = nil // Clear LastHoldStart on resume

			// Make it the asset's active call
			r.activeCalls[assetID] = call

			return nil
		}
	}

	return ErrCallNotFound
}

// EndCall terminates a call in any state, marking it as ended and setting CallEndTime.
// RevertSelectiveClaim reverts a call from pending_selective_assignment back to waiting state
func (r *inMemoryCallQueueRepository) RevertSelectiveClaim(ctx context.Context, callSID string) (bool, error) {
	// Stub implementation for in-memory repository
	r.mu.Lock()
	defer r.mu.Unlock()

	// Log that this was called
	log.Printf("In-memory RevertSelectiveClaim called for call %s (stub implementation)", callSID)

	// Always return success with no actual changes
	return true, nil
}

func (r *inMemoryCallQueueRepository) EndCall(ctx context.Context, callSID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if ctx.Err() != nil {
		return ctx.Err()
	}

	if callSID == "" {
		return ErrInvalidInput
	}

	// Check active calls
	for assetID := range r.activeCalls {
		if r.activeCalls[assetID].CallSID == callSID {
			delete(r.activeCalls, assetID)
			return nil
		}
	}

	// Check waiting calls
	for i := range r.waitingCalls {
		if r.waitingCalls[i].CallSID == callSID {
			r.waitingCalls = append(r.waitingCalls[:i], r.waitingCalls[i+1:]...)
			return nil
		}
	}

	// Check on-hold calls
	for i := range r.onHoldCalls {
		if r.onHoldCalls[i].CallSID == callSID {
			r.onHoldCalls = append(r.onHoldCalls[:i], r.onHoldCalls[i+1:]...)
			return nil
		}
	}

	return ErrCallNotFound
}

// GetActiveCall returns the current active call for an asset
func (r *inMemoryCallQueueRepository) GetActiveCall(ctx context.Context, assetID string) (QueuedCall, bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if ctx.Err() != nil {
		return QueuedCall{}, false, ctx.Err()
	}

	if assetID == "" {
		return QueuedCall{}, false, ErrInvalidInput
	}

	call, exists := r.activeCalls[assetID]
	return call, exists, nil
}

// GetHeldCalls returns all calls on hold for an asset
func (r *inMemoryCallQueueRepository) GetHeldCalls(ctx context.Context, assetID string) ([]QueuedCall, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if ctx.Err() != nil {
		return nil, ctx.Err()
	}

	if assetID == "" {
		return nil, ErrInvalidInput
	}

	var heldCalls []QueuedCall
	for i := range r.onHoldCalls {
		if r.onHoldCalls[i].AssetID == assetID {
			heldCalls = append(heldCalls, r.onHoldCalls[i])
		}
	}

	return heldCalls, nil
}

// GetCallBySituationID finds a call by its associated situation
func (r *inMemoryCallQueueRepository) GetCallBySituationID(ctx context.Context, situationID string) (QueuedCall, bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if ctx.Err() != nil {
		return QueuedCall{}, false, ctx.Err()
	}

	if situationID == "" {
		return QueuedCall{}, false, ErrInvalidInput
	}

	// Check active calls
	for assetID := range r.activeCalls {
		if r.activeCalls[assetID].SituationID == situationID {
			return r.activeCalls[assetID], true, nil
		}
	}

	// Check waiting calls
	for i := range r.waitingCalls {
		if r.waitingCalls[i].SituationID == situationID {
			return r.waitingCalls[i], true, nil
		}
	}

	// Check on-hold calls
	for i := range r.onHoldCalls {
		if r.onHoldCalls[i].SituationID == situationID {
			return r.onHoldCalls[i], true, nil
		}
	}

	return QueuedCall{}, false, nil
}

// GetQueueStatus returns current queue information
func (r *inMemoryCallQueueRepository) GetQueueStatus(ctx context.Context) (int, []QueuedCall, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if ctx.Err() != nil {
		return 0, nil, ctx.Err()
	}

	// Create defensive copy
	waitingCopy := make([]QueuedCall, len(r.waitingCalls))
	copy(waitingCopy, r.waitingCalls)

	return len(r.waitingCalls), waitingCopy, nil
}

// GetTwilioQueueSid returns the configured Twilio Queue SID.
func (r *inMemoryCallQueueRepository) GetTwilioQueueSid(ctx context.Context) (string, error) {
	// In-memory implementation just returns the configured queue SID
	return "", nil
}

// GetTwilioQueueName returns the friendly name of the Twilio queue.
func (r *inMemoryCallQueueRepository) GetTwilioQueueName(ctx context.Context) (string, error) {
	// In-memory implementation returns the SID and a default friendly name
	return "hero-default-queue", nil
}

// SetCallSituation assigns a situation to a call
func (r *inMemoryCallQueueRepository) SetCallSituation(ctx context.Context, callSID string, situationID string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if ctx.Err() != nil {
		return ctx.Err()
	}

	if callSID == "" || situationID == "" {
		return ErrInvalidInput
	}

	// Check active calls
	for id := range r.activeCalls {
		if r.activeCalls[id].CallSID == callSID {
			call := r.activeCalls[id]
			call.SituationID = situationID
			r.activeCalls[id] = call
			return nil
		}
	}

	// Check waiting calls
	for i := range r.waitingCalls {
		if r.waitingCalls[i].CallSID == callSID {
			r.waitingCalls[i].SituationID = situationID
			return nil
		}
	}

	// Check on-hold calls
	for i := range r.onHoldCalls {
		if r.onHoldCalls[i].CallSID == callSID {
			r.onHoldCalls[i].SituationID = situationID
			return nil
		}
	}

	return ErrCallNotFound
}

func (r *inMemoryCallQueueRepository) GetCallByCallSID(ctx context.Context, callSID string) (QueuedCall, bool, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	if ctx.Err() != nil {
		return QueuedCall{}, false, ctx.Err()
	}

	if callSID == "" {
		return QueuedCall{}, false, ErrInvalidInput
	}

	// Check active calls
	for assetID := range r.activeCalls {
		if r.activeCalls[assetID].CallSID == callSID {
			return r.activeCalls[assetID], true, nil
		}
	}

	// Check waiting calls
	for i := range r.waitingCalls {
		if r.waitingCalls[i].CallSID == callSID {
			return r.waitingCalls[i], true, nil
		}
	}

	// Check on-hold calls
	for i := range r.onHoldCalls {
		if r.onHoldCalls[i].CallSID == callSID {
			return r.onHoldCalls[i], true, nil
		}
	}

	return QueuedCall{}, false, nil
}

// SetQueueStrategy sets the strategy used for selecting the next call
func (r *inMemoryCallQueueRepository) SetQueueStrategy(strategy QueueStrategy) {
	r.mu.Lock()
	defer r.mu.Unlock()

	r.queueStrategy = strategy
}

func (r *inMemoryCallQueueRepository) StoreActiveCall(ctx context.Context, call QueuedCall) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if ctx.Err() != nil {
		return ctx.Err()
	}

	// Validate requirements
	if call.CallSID == "" || call.AssetID == "" {
		return ErrInvalidInput
	}

	// Ensure state is active
	if call.State != CallStateActive {
		call.State = CallStateActive
	}

	// Set CallStartTime if it's not already set (e.g., for new outbound calls)
	if call.CallStartTime == nil {
		now := time.Now()
		call.CallStartTime = &now
	}

	// Store in activeCalls map
	r.activeCalls[call.AssetID] = call

	return nil
}

func (r *inMemoryCallQueueRepository) GetOrgTwilioDetails(ctx context.Context) (OrgTwilioDetails, error) {
	return OrgTwilioDetails{
		TwimlAppSid:   os.Getenv("TWIML_APP_SID"),
		TwilioNumber:  os.Getenv("TWILIO_NUMBER"),
		OrgName:       "Default Org", // Fallback for local development
		PhoneLineId:   1,
		LineType:      "general",
		IsPrimaryLine: true,
	}, nil
}

// GetOrgTwilioDetailsByPhoneNumber retrieves org and phone line details by phone number (in-memory fallback)
func (r *inMemoryCallQueueRepository) GetOrgTwilioDetailsByPhoneNumber(ctx context.Context, phoneNumber string) (OrgPhoneLineDetails, error) {
	// For in-memory implementation, return default values
	return OrgPhoneLineDetails{
		Id:                    1,
		OrgId:                 1,
		LineType:              "general",
		TwilioReceivingNumber: phoneNumber,
		TwimlAppSid:           os.Getenv("TWIML_APP_SID"),
		IsPrimaryLine:         true,
		OrgName:               "Default Org",
		TwilioApiUserId:       "",
	}, nil
}

// GetOrgPhoneLineDetails retrieves phone line details by org ID and line type (in-memory fallback)
func (r *inMemoryCallQueueRepository) GetOrgPhoneLineDetails(ctx context.Context, orgId int32, lineType string) (OrgPhoneLineDetails, error) {
	// For in-memory implementation, return default values
	return OrgPhoneLineDetails{
		Id:                    1,
		OrgId:                 orgId,
		LineType:              lineType,
		TwilioReceivingNumber: os.Getenv("TWILIO_NUMBER"),
		TwimlAppSid:           os.Getenv("TWIML_APP_SID"),
		IsPrimaryLine:         lineType == "general",
		OrgName:               "Default Org",
		TwilioApiUserId:       "",
	}, nil
}

// ListOrgPhoneLines retrieves all phone lines for an organization (in-memory fallback)
func (r *inMemoryCallQueueRepository) ListOrgPhoneLines(ctx context.Context, orgId int32) ([]OrgPhoneLineDetails, error) {
	// For in-memory implementation, return a single default line
	return []OrgPhoneLineDetails{
		{
			Id:                    1,
			OrgId:                 orgId,
			LineType:              "general",
			TwilioReceivingNumber: os.Getenv("TWILIO_NUMBER"),
			TwimlAppSid:           os.Getenv("TWIML_APP_SID"),
			IsPrimaryLine:         true,
			OrgName:               "Default Org",
			TwilioApiUserId:       "",
		},
	}, nil
}

// Holder function
func (r *inMemoryCallQueueRepository) DequeueCallBySid(ctx context.Context, callSID string, agentID string) (QueuedCall, bool, error) {
	return QueuedCall{}, false, nil
}
