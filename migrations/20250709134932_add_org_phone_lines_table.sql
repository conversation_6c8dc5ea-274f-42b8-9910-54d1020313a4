-- +goose Up
-- +goose StatementBegin
-- Create org_phone_lines table to support multiple phone numbers per organization
CREATE TABLE org_phone_lines (
    id SERIAL PRIMARY KEY,
    org_id INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    line_type TEXT NOT NULL,                           -- Flexible: emergency, general, parking, etc.
    external_forwarding_number TEXT,                   -- Customer's number that forwards to us (optional)
    twilio_receiving_number TEXT NOT NULL,             -- Our Twilio number that receives calls
    twilio_number_sid TEXT,                            -- Twilio's SID for API operations
    twiml_app_sid TEXT,                                -- TwiML application SID for call handling
    is_primary_line BOOLEAN DEFAULT FALSE,             -- Primary line for backwards compatibility
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    -- Constraints
    UNIQUE(twilio_receiving_number),                   -- Each Twilio number used once globally
    UNIQUE(org_id, external_forwarding_number)        -- Each org's external number used once (if provided)
);
-- Indexes for performance
CREATE INDEX idx_org_phone_lines_twilio_receiving_number ON org_phone_lines(twilio_receiving_number);
CREATE INDEX idx_org_phone_lines_org_id ON org_phone_lines(org_id);
CREATE INDEX idx_org_phone_lines_org_id_line_type ON org_phone_lines(org_id, line_type);
-- Row Level Security (following existing pattern)
ALTER TABLE org_phone_lines ENABLE ROW LEVEL SECURITY;
CREATE POLICY "org_phone_lines_organization_access" ON org_phone_lines
    USING (org_id::text = current_setting('app.org_id'))
    WITH CHECK (org_id::text = current_setting('app.org_id'));
CREATE POLICY "org_phone_lines_meta_org_access" ON org_phone_lines
    USING (current_setting('app.org_id') = '-1')
    WITH CHECK (current_setting('app.org_id') = '-1');
-- Migrate existing data from orgs table to org_phone_lines
-- Set existing twilio_number as primary "general" line with twiml_app_sid
INSERT INTO org_phone_lines (org_id, line_type, twilio_receiving_number, twilio_number_sid, twiml_app_sid, is_primary_line)
SELECT id, 'general', twilio_number, twilio_number_sid, twiml_app_sid, true
FROM orgs
WHERE twilio_number IS NOT NULL AND twilio_number != '' AND  id >= 0;
-- Clean up orgs table by removing unused/dead columns
ALTER TABLE orgs DROP COLUMN IF EXISTS forwarded_from_number;
ALTER TABLE orgs DROP COLUMN IF EXISTS call_forwarding_type;
ALTER TABLE orgs DROP COLUMN IF EXISTS is_call_forwarding_enabled;
ALTER TABLE orgs DROP COLUMN IF EXISTS sip_uri;
-- Move Twilio routing data to org_phone_lines table (single source of truth)
ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number;
ALTER TABLE orgs DROP COLUMN IF EXISTS twilio_number_sid;
ALTER TABLE orgs DROP COLUMN IF EXISTS twiml_app_sid;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Remove policies and disable RLS
DROP POLICY IF EXISTS "org_phone_lines_organization_access" ON org_phone_lines;
DROP POLICY IF EXISTS "org_phone_lines_meta_org_access" ON org_phone_lines;
ALTER TABLE org_phone_lines DISABLE ROW LEVEL SECURITY;
-- Drop indexes
DROP INDEX IF EXISTS idx_org_phone_lines_twilio_receiving_number;
DROP INDEX IF EXISTS idx_org_phone_lines_org_id;
DROP INDEX IF EXISTS idx_org_phone_lines_org_id_line_type;
-- Drop table
DROP TABLE IF EXISTS org_phone_lines;
-- Restore removed columns (with default values)
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS forwarded_from_number TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS call_forwarding_type TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS is_call_forwarding_enabled BOOLEAN DEFAULT FALSE;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS sip_uri TEXT;
-- Restore Twilio columns
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS twilio_number TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS twilio_number_sid TEXT;
ALTER TABLE orgs ADD COLUMN IF NOT EXISTS twiml_app_sid TEXT;
-- +goose StatementEnd