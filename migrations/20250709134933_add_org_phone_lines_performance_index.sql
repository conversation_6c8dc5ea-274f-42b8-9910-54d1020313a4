-- +goose Up
-- +goose StatementBegin
-- Add composite index for optimal JOIN performance on org_id and is_primary_line
-- This index will significantly improve performance for queries that JOIN orgs with org_phone_lines
-- to find the primary line for each organization
CREATE INDEX idx_org_phone_lines_org_id_is_primary ON org_phone_lines(org_id, is_primary_line);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Remove the composite index
DROP INDEX IF EXISTS idx_org_phone_lines_org_id_is_primary;
-- +goose StatementEnd
