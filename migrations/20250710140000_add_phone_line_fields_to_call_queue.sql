-- +goose Up
-- +goose StatementBegin
-- Add phone line identification fields to call_queue table for multi-line support
ALTER TABLE call_queue 
ADD COLUMN phone_line_id INTEGER REFERENCES org_phone_lines(id) ON DELETE SET NULL,
ADD COLUMN twilio_receiving_number TEXT,
ADD COLUMN line_type TEXT DEFAULT 'general';

-- Create index for efficient phone line lookups
CREATE INDEX idx_call_queue_phone_line_id ON call_queue(phone_line_id);
CREATE INDEX idx_call_queue_twilio_receiving_number ON call_queue(twilio_receiving_number);
CREATE INDEX idx_call_queue_line_type ON call_queue(line_type);

-- Backfill existing calls with primary line data where possible
UPDATE call_queue 
SET 
    phone_line_id = opl.id,
    twilio_receiving_number = opl.twilio_receiving_number,
    line_type = opl.line_type
FROM org_phone_lines opl
WHERE call_queue.org_id = opl.org_id 
AND opl.is_primary_line = true
AND call_queue.phone_line_id IS NULL;
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
-- Remove phone line fields from call_queue table
DROP INDEX IF EXISTS idx_call_queue_line_type;
DROP INDEX IF EXISTS idx_call_queue_twilio_receiving_number;
DROP INDEX IF EXISTS idx_call_queue_phone_line_id;

ALTER TABLE call_queue 
DROP COLUMN IF EXISTS line_type,
DROP COLUMN IF EXISTS twilio_receiving_number,
DROP COLUMN IF EXISTS phone_line_id;
-- +goose StatementEnd
