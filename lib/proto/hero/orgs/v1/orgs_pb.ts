// @generated by protoc-gen-es v2.5.2 with parameter "target=ts"
// @generated from file hero/orgs/v1/orgs.proto (package hero.orgs.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_hero_permissions_v1_permissions } from "../../permissions/v1/permissions_pb";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/orgs/v1/orgs.proto.
 */
export const file_hero_orgs_v1_orgs: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_timestamp, file_hero_permissions_v1_permissions]);

/**
 * @generated from message hero.orgs.v1.TurnOnGuestModeRequest
 */
export type TurnOnGuestModeRequest = Message<"hero.orgs.v1.TurnOnGuestModeRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.TurnOnGuestModeRequest.
 * Use `create(TurnOnGuestModeRequestSchema)` to create a new message.
 */
export const TurnOnGuestModeRequestSchema: GenMessage<TurnOnGuestModeRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 0);

/**
 * @generated from message hero.orgs.v1.TurnOnGuestModeResponse
 */
export type TurnOnGuestModeResponse = Message<"hero.orgs.v1.TurnOnGuestModeResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.orgs.v1.TurnOnGuestModeResponse.
 * Use `create(TurnOnGuestModeResponseSchema)` to create a new message.
 */
export const TurnOnGuestModeResponseSchema: GenMessage<TurnOnGuestModeResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 1);

/**
 * @generated from message hero.orgs.v1.TurnOffGuestModeRequest
 */
export type TurnOffGuestModeRequest = Message<"hero.orgs.v1.TurnOffGuestModeRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.TurnOffGuestModeRequest.
 * Use `create(TurnOffGuestModeRequestSchema)` to create a new message.
 */
export const TurnOffGuestModeRequestSchema: GenMessage<TurnOffGuestModeRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 2);

/**
 * @generated from message hero.orgs.v1.TurnOffGuestModeResponse
 */
export type TurnOffGuestModeResponse = Message<"hero.orgs.v1.TurnOffGuestModeResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;
};

/**
 * Describes the message hero.orgs.v1.TurnOffGuestModeResponse.
 * Use `create(TurnOffGuestModeResponseSchema)` to create a new message.
 */
export const TurnOffGuestModeResponseSchema: GenMessage<TurnOffGuestModeResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 3);

/**
 * @generated from message hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest
 */
export type GetOrgAPIUserPrivateByIdRequest = Message<"hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;
};

/**
 * Describes the message hero.orgs.v1.GetOrgAPIUserPrivateByIdRequest.
 * Use `create(GetOrgAPIUserPrivateByIdRequestSchema)` to create a new message.
 */
export const GetOrgAPIUserPrivateByIdRequestSchema: GenMessage<GetOrgAPIUserPrivateByIdRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 4);

/**
 * @generated from message hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse
 */
export type GetOrgAPIUserPrivateByIdResponse = Message<"hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgApiUserPrivate org_api_user = 1;
   */
  orgApiUser?: OrgApiUserPrivate;
};

/**
 * Describes the message hero.orgs.v1.GetOrgAPIUserPrivateByIdResponse.
 * Use `create(GetOrgAPIUserPrivateByIdResponseSchema)` to create a new message.
 */
export const GetOrgAPIUserPrivateByIdResponseSchema: GenMessage<GetOrgAPIUserPrivateByIdResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 5);

/**
 * @generated from message hero.orgs.v1.CreateOrgRequest
 */
export type CreateOrgRequest = Message<"hero.orgs.v1.CreateOrgRequest"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgRequest.
 * Use `create(CreateOrgRequestSchema)` to create a new message.
 */
export const CreateOrgRequestSchema: GenMessage<CreateOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 6);

/**
 * @generated from message hero.orgs.v1.CreateOrgResponse
 */
export type CreateOrgResponse = Message<"hero.orgs.v1.CreateOrgResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgResponse.
 * Use `create(CreateOrgResponseSchema)` to create a new message.
 */
export const CreateOrgResponseSchema: GenMessage<CreateOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 7);

/**
 * @generated from message hero.orgs.v1.UpdateOrgRequest
 */
export type UpdateOrgRequest = Message<"hero.orgs.v1.UpdateOrgRequest"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.UpdateOrgRequest.
 * Use `create(UpdateOrgRequestSchema)` to create a new message.
 */
export const UpdateOrgRequestSchema: GenMessage<UpdateOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 8);

/**
 * @generated from message hero.orgs.v1.UpdateOrgResponse
 */
export type UpdateOrgResponse = Message<"hero.orgs.v1.UpdateOrgResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.UpdateOrgResponse.
 * Use `create(UpdateOrgResponseSchema)` to create a new message.
 */
export const UpdateOrgResponseSchema: GenMessage<UpdateOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 9);

/**
 * @generated from message hero.orgs.v1.DeleteOrgRequest
 */
export type DeleteOrgRequest = Message<"hero.orgs.v1.DeleteOrgRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hero.orgs.v1.DeleteOrgRequest.
 * Use `create(DeleteOrgRequestSchema)` to create a new message.
 */
export const DeleteOrgRequestSchema: GenMessage<DeleteOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 10);

/**
 * @generated from message hero.orgs.v1.DeleteOrgResponse
 */
export type DeleteOrgResponse = Message<"hero.orgs.v1.DeleteOrgResponse"> & {
};

/**
 * Describes the message hero.orgs.v1.DeleteOrgResponse.
 * Use `create(DeleteOrgResponseSchema)` to create a new message.
 */
export const DeleteOrgResponseSchema: GenMessage<DeleteOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 11);

/**
 * @generated from message hero.orgs.v1.GetOrgRequest
 */
export type GetOrgRequest = Message<"hero.orgs.v1.GetOrgRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hero.orgs.v1.GetOrgRequest.
 * Use `create(GetOrgRequestSchema)` to create a new message.
 */
export const GetOrgRequestSchema: GenMessage<GetOrgRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 12);

/**
 * @generated from message hero.orgs.v1.GetOrgResponse
 */
export type GetOrgResponse = Message<"hero.orgs.v1.GetOrgResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.Org org = 1;
   */
  org?: Org;
};

/**
 * Describes the message hero.orgs.v1.GetOrgResponse.
 * Use `create(GetOrgResponseSchema)` to create a new message.
 */
export const GetOrgResponseSchema: GenMessage<GetOrgResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 13);

/**
 * @generated from message hero.orgs.v1.ListOrgsRequest
 */
export type ListOrgsRequest = Message<"hero.orgs.v1.ListOrgsRequest"> & {
};

/**
 * Describes the message hero.orgs.v1.ListOrgsRequest.
 * Use `create(ListOrgsRequestSchema)` to create a new message.
 */
export const ListOrgsRequestSchema: GenMessage<ListOrgsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 14);

/**
 * @generated from message hero.orgs.v1.ListOrgsResponse
 */
export type ListOrgsResponse = Message<"hero.orgs.v1.ListOrgsResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.Org orgs = 1;
   */
  orgs: Org[];
};

/**
 * Describes the message hero.orgs.v1.ListOrgsResponse.
 * Use `create(ListOrgsResponseSchema)` to create a new message.
 */
export const ListOrgsResponseSchema: GenMessage<ListOrgsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 15);

/**
 * @generated from message hero.orgs.v1.ValidateOrgCredsRequest
 */
export type ValidateOrgCredsRequest = Message<"hero.orgs.v1.ValidateOrgCredsRequest"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string password = 2;
   */
  password: string;
};

/**
 * Describes the message hero.orgs.v1.ValidateOrgCredsRequest.
 * Use `create(ValidateOrgCredsRequestSchema)` to create a new message.
 */
export const ValidateOrgCredsRequestSchema: GenMessage<ValidateOrgCredsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 16);

/**
 * @generated from message hero.orgs.v1.ValidateOrgCredsResponse
 */
export type ValidateOrgCredsResponse = Message<"hero.orgs.v1.ValidateOrgCredsResponse"> & {
  /**
   * @generated from field: bool valid = 1;
   */
  valid: boolean;

  /**
   * @generated from field: hero.orgs.v1.OrgApiUser org_api_user = 2;
   */
  orgApiUser?: OrgApiUser;
};

/**
 * Describes the message hero.orgs.v1.ValidateOrgCredsResponse.
 * Use `create(ValidateOrgCredsResponseSchema)` to create a new message.
 */
export const ValidateOrgCredsResponseSchema: GenMessage<ValidateOrgCredsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 17);

/**
 * @generated from message hero.orgs.v1.OrgApiUser
 */
export type OrgApiUser = Message<"hero.orgs.v1.OrgApiUser"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string username = 3;
   */
  username: string;

  /**
   * @generated from field: string encrypted_password = 4;
   */
  encryptedPassword: string;

  /**
   * @generated from field: string hashed_password = 5;
   */
  hashedPassword: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 7;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.OrgApiUser.
 * Use `create(OrgApiUserSchema)` to create a new message.
 */
export const OrgApiUserSchema: GenMessage<OrgApiUser> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 18);

/**
 * @generated from message hero.orgs.v1.OrgApiUserPrivate
 */
export type OrgApiUserPrivate = Message<"hero.orgs.v1.OrgApiUserPrivate"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string username = 3;
   */
  username: string;

  /**
   * @generated from field: string encrypted_password = 4;
   */
  encryptedPassword: string;

  /**
   * @generated from field: string hashed_password = 5;
   */
  hashedPassword: string;

  /**
   * @generated from field: string raw_password = 6;
   */
  rawPassword: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 7;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 8;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.OrgApiUserPrivate.
 * Use `create(OrgApiUserPrivateSchema)` to create a new message.
 */
export const OrgApiUserPrivateSchema: GenMessage<OrgApiUserPrivate> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 19);

/**
 * @generated from message hero.orgs.v1.OrgQueue
 */
export type OrgQueue = Message<"hero.orgs.v1.OrgQueue"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string friendly_name = 2;
   */
  friendlyName: string;

  /**
   * @generated from field: string twilio_queue_sid = 3;
   */
  twilioQueueSid: string;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 6;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: int32 org_id = 7;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.OrgQueue.
 * Use `create(OrgQueueSchema)` to create a new message.
 */
export const OrgQueueSchema: GenMessage<OrgQueue> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 20);

/**
 * @generated from message hero.orgs.v1.InsertOrgQueueRequest
 */
export type InsertOrgQueueRequest = Message<"hero.orgs.v1.InsertOrgQueueRequest"> & {
  /**
   * @generated from field: string friendly_name = 1;
   */
  friendlyName: string;

  /**
   * @generated from field: string twilio_queue_sid = 2;
   */
  twilioQueueSid: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: int32 org_id = 4;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.InsertOrgQueueRequest.
 * Use `create(InsertOrgQueueRequestSchema)` to create a new message.
 */
export const InsertOrgQueueRequestSchema: GenMessage<InsertOrgQueueRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 21);

/**
 * @generated from message hero.orgs.v1.InsertOrgQueueResponse
 */
export type InsertOrgQueueResponse = Message<"hero.orgs.v1.InsertOrgQueueResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgQueue org_queue = 1;
   */
  orgQueue?: OrgQueue;
};

/**
 * Describes the message hero.orgs.v1.InsertOrgQueueResponse.
 * Use `create(InsertOrgQueueResponseSchema)` to create a new message.
 */
export const InsertOrgQueueResponseSchema: GenMessage<InsertOrgQueueResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 22);

/**
 * @generated from message hero.orgs.v1.CreateOrgTwilioQueueRequest
 */
export type CreateOrgTwilioQueueRequest = Message<"hero.orgs.v1.CreateOrgTwilioQueueRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string friendly_name = 2;
   */
  friendlyName: string;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgTwilioQueueRequest.
 * Use `create(CreateOrgTwilioQueueRequestSchema)` to create a new message.
 */
export const CreateOrgTwilioQueueRequestSchema: GenMessage<CreateOrgTwilioQueueRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 23);

/**
 * @generated from message hero.orgs.v1.CreateOrgTwilioQueueResponse
 */
export type CreateOrgTwilioQueueResponse = Message<"hero.orgs.v1.CreateOrgTwilioQueueResponse"> & {
  /**
   * @generated from field: string queue_sid = 1;
   */
  queueSid: string;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgTwilioQueueResponse.
 * Use `create(CreateOrgTwilioQueueResponseSchema)` to create a new message.
 */
export const CreateOrgTwilioQueueResponseSchema: GenMessage<CreateOrgTwilioQueueResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 24);

/**
 * @generated from message hero.orgs.v1.Org
 */
export type Org = Message<"hero.orgs.v1.Org"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: repeated string domains = 3;
   */
  domains: string[];

  /**
   * @generated from field: string twiml_app_sid = 4;
   */
  twimlAppSid: string;

  /**
   * Primary Twilio number for backwards compatibility
   *
   * @generated from field: string twilio_number = 5;
   */
  twilioNumber: string;

  /**
   * Twilio SID for primary number
   *
   * @generated from field: string twilio_number_sid = 6;
   */
  twilioNumberSid: string;

  /**
   * @generated from field: hero.orgs.v1.ServiceType service_type = 7;
   */
  serviceType: ServiceType;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 8;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 9;
   */
  updatedAt?: Timestamp;

  /**
   * ID of the template used to setup the org's features
   *
   * @generated from field: string template_id = 10;
   */
  templateId: string;

  /**
   * The primary phone number for the organization
   *
   * @generated from field: string primary_phone_number = 11;
   */
  primaryPhoneNumber: string;
};

/**
 * Describes the message hero.orgs.v1.Org.
 * Use `create(OrgSchema)` to create a new message.
 */
export const OrgSchema: GenMessage<Org> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 25);

/**
 * @generated from message hero.orgs.v1.CreateOrgAPIUserRequest
 */
export type CreateOrgAPIUserRequest = Message<"hero.orgs.v1.CreateOrgAPIUserRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgAPIUserRequest.
 * Use `create(CreateOrgAPIUserRequestSchema)` to create a new message.
 */
export const CreateOrgAPIUserRequestSchema: GenMessage<CreateOrgAPIUserRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 26);

/**
 * @generated from message hero.orgs.v1.CreateOrgAPIUserResponse
 */
export type CreateOrgAPIUserResponse = Message<"hero.orgs.v1.CreateOrgAPIUserResponse"> & {
  /**
   * @generated from field: string username = 1;
   */
  username: string;

  /**
   * @generated from field: string encrypted_password = 2;
   */
  encryptedPassword: string;

  /**
   * @generated from field: string hashed_password = 3;
   */
  hashedPassword: string;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgAPIUserResponse.
 * Use `create(CreateOrgAPIUserResponseSchema)` to create a new message.
 */
export const CreateOrgAPIUserResponseSchema: GenMessage<CreateOrgAPIUserResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 27);

/**
 * @generated from message hero.orgs.v1.GetZelloChannelsRequest
 */
export type GetZelloChannelsRequest = Message<"hero.orgs.v1.GetZelloChannelsRequest"> & {
};

/**
 * Describes the message hero.orgs.v1.GetZelloChannelsRequest.
 * Use `create(GetZelloChannelsRequestSchema)` to create a new message.
 */
export const GetZelloChannelsRequestSchema: GenMessage<GetZelloChannelsRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 28);

/**
 * @generated from message hero.orgs.v1.GetZelloChannelsResponse
 */
export type GetZelloChannelsResponse = Message<"hero.orgs.v1.GetZelloChannelsResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.ZelloChannel zello_channels = 1;
   */
  zelloChannels: ZelloChannel[];
};

/**
 * Describes the message hero.orgs.v1.GetZelloChannelsResponse.
 * Use `create(GetZelloChannelsResponseSchema)` to create a new message.
 */
export const GetZelloChannelsResponseSchema: GenMessage<GetZelloChannelsResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 29);

/**
 * @generated from message hero.orgs.v1.ZelloChannel
 */
export type ZelloChannel = Message<"hero.orgs.v1.ZelloChannel"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string zello_channel_id = 3;
   */
  zelloChannelId: string;

  /**
   * @generated from field: string display_name = 4;
   */
  displayName: string;
};

/**
 * Describes the message hero.orgs.v1.ZelloChannel.
 * Use `create(ZelloChannelSchema)` to create a new message.
 */
export const ZelloChannelSchema: GenMessage<ZelloChannel> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 30);

/**
 * @generated from message hero.orgs.v1.CreateCognitoUserRequest
 */
export type CreateCognitoUserRequest = Message<"hero.orgs.v1.CreateCognitoUserRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string username = 2;
   */
  username: string;

  /**
   * @generated from field: string email = 3;
   */
  email: string;

  /**
   * @generated from field: string password = 4;
   */
  password: string;
};

/**
 * Describes the message hero.orgs.v1.CreateCognitoUserRequest.
 * Use `create(CreateCognitoUserRequestSchema)` to create a new message.
 */
export const CreateCognitoUserRequestSchema: GenMessage<CreateCognitoUserRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 31);

/**
 * @generated from message hero.orgs.v1.CreateCognitoUserResponse
 */
export type CreateCognitoUserResponse = Message<"hero.orgs.v1.CreateCognitoUserResponse"> & {
  /**
   * @generated from field: bool success = 1;
   */
  success: boolean;

  /**
   * The Cognito user ID (sub)
   *
   * @generated from field: string cognito_sub_id = 2;
   */
  cognitoSubId: string;
};

/**
 * Describes the message hero.orgs.v1.CreateCognitoUserResponse.
 * Use `create(CreateCognitoUserResponseSchema)` to create a new message.
 */
export const CreateCognitoUserResponseSchema: GenMessage<CreateCognitoUserResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 32);

/**
 * Organization Contact Book Messages
 *
 * @generated from message hero.orgs.v1.ContactRecord
 */
export type ContactRecord = Message<"hero.orgs.v1.ContactRecord"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: string phone = 4;
   */
  phone: string;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 5;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 6;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.ContactRecord.
 * Use `create(ContactRecordSchema)` to create a new message.
 */
export const ContactRecordSchema: GenMessage<ContactRecord> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 33);

/**
 * @generated from message hero.orgs.v1.AddToContactBookRequest
 */
export type AddToContactBookRequest = Message<"hero.orgs.v1.AddToContactBookRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string phone = 3;
   */
  phone: string;
};

/**
 * Describes the message hero.orgs.v1.AddToContactBookRequest.
 * Use `create(AddToContactBookRequestSchema)` to create a new message.
 */
export const AddToContactBookRequestSchema: GenMessage<AddToContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 34);

/**
 * @generated from message hero.orgs.v1.AddToContactBookResponse
 */
export type AddToContactBookResponse = Message<"hero.orgs.v1.AddToContactBookResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.AddToContactBookResponse.
 * Use `create(AddToContactBookResponseSchema)` to create a new message.
 */
export const AddToContactBookResponseSchema: GenMessage<AddToContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 35);

/**
 * @generated from message hero.orgs.v1.UpdateContactInContactBookRequest
 */
export type UpdateContactInContactBookRequest = Message<"hero.orgs.v1.UpdateContactInContactBookRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string phone = 3;
   */
  phone: string;
};

/**
 * Describes the message hero.orgs.v1.UpdateContactInContactBookRequest.
 * Use `create(UpdateContactInContactBookRequestSchema)` to create a new message.
 */
export const UpdateContactInContactBookRequestSchema: GenMessage<UpdateContactInContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 36);

/**
 * @generated from message hero.orgs.v1.UpdateContactInContactBookResponse
 */
export type UpdateContactInContactBookResponse = Message<"hero.orgs.v1.UpdateContactInContactBookResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.UpdateContactInContactBookResponse.
 * Use `create(UpdateContactInContactBookResponseSchema)` to create a new message.
 */
export const UpdateContactInContactBookResponseSchema: GenMessage<UpdateContactInContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 37);

/**
 * @generated from message hero.orgs.v1.DeleteFromContactBookRequest
 */
export type DeleteFromContactBookRequest = Message<"hero.orgs.v1.DeleteFromContactBookRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orgs.v1.DeleteFromContactBookRequest.
 * Use `create(DeleteFromContactBookRequestSchema)` to create a new message.
 */
export const DeleteFromContactBookRequestSchema: GenMessage<DeleteFromContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 38);

/**
 * @generated from message hero.orgs.v1.DeleteFromContactBookResponse
 */
export type DeleteFromContactBookResponse = Message<"hero.orgs.v1.DeleteFromContactBookResponse"> & {
};

/**
 * Describes the message hero.orgs.v1.DeleteFromContactBookResponse.
 * Use `create(DeleteFromContactBookResponseSchema)` to create a new message.
 */
export const DeleteFromContactBookResponseSchema: GenMessage<DeleteFromContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 39);

/**
 * @generated from message hero.orgs.v1.GetContactFromContactBookRequest
 */
export type GetContactFromContactBookRequest = Message<"hero.orgs.v1.GetContactFromContactBookRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message hero.orgs.v1.GetContactFromContactBookRequest.
 * Use `create(GetContactFromContactBookRequestSchema)` to create a new message.
 */
export const GetContactFromContactBookRequestSchema: GenMessage<GetContactFromContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 40);

/**
 * @generated from message hero.orgs.v1.GetContactFromContactBookResponse
 */
export type GetContactFromContactBookResponse = Message<"hero.orgs.v1.GetContactFromContactBookResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.GetContactFromContactBookResponse.
 * Use `create(GetContactFromContactBookResponseSchema)` to create a new message.
 */
export const GetContactFromContactBookResponseSchema: GenMessage<GetContactFromContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 41);

/**
 * @generated from message hero.orgs.v1.ListContactsInContactBookRequest
 */
export type ListContactsInContactBookRequest = Message<"hero.orgs.v1.ListContactsInContactBookRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * Token for pagination (empty for first page)
   *
   * @generated from field: string page_token = 2;
   */
  pageToken: string;

  /**
   * Number of contacts to return (max 100)
   *
   * @generated from field: int32 page_size = 3;
   */
  pageSize: number;
};

/**
 * Describes the message hero.orgs.v1.ListContactsInContactBookRequest.
 * Use `create(ListContactsInContactBookRequestSchema)` to create a new message.
 */
export const ListContactsInContactBookRequestSchema: GenMessage<ListContactsInContactBookRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 42);

/**
 * @generated from message hero.orgs.v1.ListContactsInContactBookResponse
 */
export type ListContactsInContactBookResponse = Message<"hero.orgs.v1.ListContactsInContactBookResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.ContactRecord contacts = 1;
   */
  contacts: ContactRecord[];

  /**
   * Token for next page (empty if no more pages)
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;

  /**
   * Total number of contacts in the contact book
   *
   * @generated from field: int32 total_count = 3;
   */
  totalCount: number;
};

/**
 * Describes the message hero.orgs.v1.ListContactsInContactBookResponse.
 * Use `create(ListContactsInContactBookResponseSchema)` to create a new message.
 */
export const ListContactsInContactBookResponseSchema: GenMessage<ListContactsInContactBookResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 43);

/**
 * @generated from message hero.orgs.v1.GetContactByPhoneNumberRequest
 */
export type GetContactByPhoneNumberRequest = Message<"hero.orgs.v1.GetContactByPhoneNumberRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string phone = 2;
   */
  phone: string;
};

/**
 * Describes the message hero.orgs.v1.GetContactByPhoneNumberRequest.
 * Use `create(GetContactByPhoneNumberRequestSchema)` to create a new message.
 */
export const GetContactByPhoneNumberRequestSchema: GenMessage<GetContactByPhoneNumberRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 44);

/**
 * @generated from message hero.orgs.v1.GetContactByPhoneNumberResponse
 */
export type GetContactByPhoneNumberResponse = Message<"hero.orgs.v1.GetContactByPhoneNumberResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.ContactRecord contact = 1;
   */
  contact?: ContactRecord;
};

/**
 * Describes the message hero.orgs.v1.GetContactByPhoneNumberResponse.
 * Use `create(GetContactByPhoneNumberResponseSchema)` to create a new message.
 */
export const GetContactByPhoneNumberResponseSchema: GenMessage<GetContactByPhoneNumberResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 45);

/**
 * Phone Line Management Messages
 *
 * @generated from message hero.orgs.v1.OrgPhoneLine
 */
export type OrgPhoneLine = Message<"hero.orgs.v1.OrgPhoneLine"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * emergency, general, parking, etc.
   *
   * @generated from field: string line_type = 3;
   */
  lineType: string;

  /**
   * Customer's number that forwards to us (optional)
   *
   * @generated from field: string external_forwarding_number = 4;
   */
  externalForwardingNumber: string;

  /**
   * Our Twilio number that receives calls
   *
   * @generated from field: string twilio_receiving_number = 5;
   */
  twilioReceivingNumber: string;

  /**
   * Twilio's SID for API operations
   *
   * @generated from field: string twilio_number_sid = 6;
   */
  twilioNumberSid: string;

  /**
   * TwiML application SID for call handling
   *
   * @generated from field: string twiml_app_sid = 7;
   */
  twimlAppSid: string;

  /**
   * Primary line for backwards compatibility
   *
   * @generated from field: bool is_primary_line = 8;
   */
  isPrimaryLine: boolean;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 9;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 10;
   */
  updatedAt?: Timestamp;
};

/**
 * Describes the message hero.orgs.v1.OrgPhoneLine.
 * Use `create(OrgPhoneLineSchema)` to create a new message.
 */
export const OrgPhoneLineSchema: GenMessage<OrgPhoneLine> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 46);

/**
 * @generated from message hero.orgs.v1.CreateOrgPhoneLineRequest
 */
export type CreateOrgPhoneLineRequest = Message<"hero.orgs.v1.CreateOrgPhoneLineRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: string line_type = 2;
   */
  lineType: string;

  /**
   * @generated from field: string external_forwarding_number = 3;
   */
  externalForwardingNumber: string;

  /**
   * @generated from field: string twilio_receiving_number = 4;
   */
  twilioReceivingNumber: string;

  /**
   * @generated from field: string twilio_number_sid = 5;
   */
  twilioNumberSid: string;

  /**
   * @generated from field: string twiml_app_sid = 6;
   */
  twimlAppSid: string;

  /**
   * @generated from field: bool is_primary_line = 7;
   */
  isPrimaryLine: boolean;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgPhoneLineRequest.
 * Use `create(CreateOrgPhoneLineRequestSchema)` to create a new message.
 */
export const CreateOrgPhoneLineRequestSchema: GenMessage<CreateOrgPhoneLineRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 47);

/**
 * @generated from message hero.orgs.v1.CreateOrgPhoneLineResponse
 */
export type CreateOrgPhoneLineResponse = Message<"hero.orgs.v1.CreateOrgPhoneLineResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgPhoneLine org_phone_line = 1;
   */
  orgPhoneLine?: OrgPhoneLine;
};

/**
 * Describes the message hero.orgs.v1.CreateOrgPhoneLineResponse.
 * Use `create(CreateOrgPhoneLineResponseSchema)` to create a new message.
 */
export const CreateOrgPhoneLineResponseSchema: GenMessage<CreateOrgPhoneLineResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 48);

/**
 * @generated from message hero.orgs.v1.GetOrgPhoneLineByTwilioNumberRequest
 */
export type GetOrgPhoneLineByTwilioNumberRequest = Message<"hero.orgs.v1.GetOrgPhoneLineByTwilioNumberRequest"> & {
  /**
   * @generated from field: string twilio_receiving_number = 1;
   */
  twilioReceivingNumber: string;
};

/**
 * Describes the message hero.orgs.v1.GetOrgPhoneLineByTwilioNumberRequest.
 * Use `create(GetOrgPhoneLineByTwilioNumberRequestSchema)` to create a new message.
 */
export const GetOrgPhoneLineByTwilioNumberRequestSchema: GenMessage<GetOrgPhoneLineByTwilioNumberRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 49);

/**
 * @generated from message hero.orgs.v1.GetOrgPhoneLineByTwilioNumberResponse
 */
export type GetOrgPhoneLineByTwilioNumberResponse = Message<"hero.orgs.v1.GetOrgPhoneLineByTwilioNumberResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgPhoneLine org_phone_line = 1;
   */
  orgPhoneLine?: OrgPhoneLine;

  /**
   * @generated from field: bool found = 2;
   */
  found: boolean;
};

/**
 * Describes the message hero.orgs.v1.GetOrgPhoneLineByTwilioNumberResponse.
 * Use `create(GetOrgPhoneLineByTwilioNumberResponseSchema)` to create a new message.
 */
export const GetOrgPhoneLineByTwilioNumberResponseSchema: GenMessage<GetOrgPhoneLineByTwilioNumberResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 50);

/**
 * @generated from message hero.orgs.v1.ListOrgPhoneLinesRequest
 */
export type ListOrgPhoneLinesRequest = Message<"hero.orgs.v1.ListOrgPhoneLinesRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;
};

/**
 * Describes the message hero.orgs.v1.ListOrgPhoneLinesRequest.
 * Use `create(ListOrgPhoneLinesRequestSchema)` to create a new message.
 */
export const ListOrgPhoneLinesRequestSchema: GenMessage<ListOrgPhoneLinesRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 51);

/**
 * @generated from message hero.orgs.v1.ListOrgPhoneLinesResponse
 */
export type ListOrgPhoneLinesResponse = Message<"hero.orgs.v1.ListOrgPhoneLinesResponse"> & {
  /**
   * @generated from field: repeated hero.orgs.v1.OrgPhoneLine org_phone_lines = 1;
   */
  orgPhoneLines: OrgPhoneLine[];
};

/**
 * Describes the message hero.orgs.v1.ListOrgPhoneLinesResponse.
 * Use `create(ListOrgPhoneLinesResponseSchema)` to create a new message.
 */
export const ListOrgPhoneLinesResponseSchema: GenMessage<ListOrgPhoneLinesResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 52);

/**
 * @generated from message hero.orgs.v1.UpdateOrgPhoneLineRequest
 */
export type UpdateOrgPhoneLineRequest = Message<"hero.orgs.v1.UpdateOrgPhoneLineRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;

  /**
   * @generated from field: string line_type = 2;
   */
  lineType: string;

  /**
   * @generated from field: string external_forwarding_number = 3;
   */
  externalForwardingNumber: string;

  /**
   * @generated from field: string twilio_receiving_number = 4;
   */
  twilioReceivingNumber: string;

  /**
   * @generated from field: string twilio_number_sid = 5;
   */
  twilioNumberSid: string;

  /**
   * @generated from field: string twiml_app_sid = 6;
   */
  twimlAppSid: string;

  /**
   * @generated from field: bool is_primary_line = 7;
   */
  isPrimaryLine: boolean;
};

/**
 * Describes the message hero.orgs.v1.UpdateOrgPhoneLineRequest.
 * Use `create(UpdateOrgPhoneLineRequestSchema)` to create a new message.
 */
export const UpdateOrgPhoneLineRequestSchema: GenMessage<UpdateOrgPhoneLineRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 53);

/**
 * @generated from message hero.orgs.v1.UpdateOrgPhoneLineResponse
 */
export type UpdateOrgPhoneLineResponse = Message<"hero.orgs.v1.UpdateOrgPhoneLineResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgPhoneLine org_phone_line = 1;
   */
  orgPhoneLine?: OrgPhoneLine;
};

/**
 * Describes the message hero.orgs.v1.UpdateOrgPhoneLineResponse.
 * Use `create(UpdateOrgPhoneLineResponseSchema)` to create a new message.
 */
export const UpdateOrgPhoneLineResponseSchema: GenMessage<UpdateOrgPhoneLineResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 54);

/**
 * @generated from message hero.orgs.v1.DeleteOrgPhoneLineRequest
 */
export type DeleteOrgPhoneLineRequest = Message<"hero.orgs.v1.DeleteOrgPhoneLineRequest"> & {
  /**
   * @generated from field: int32 id = 1;
   */
  id: number;
};

/**
 * Describes the message hero.orgs.v1.DeleteOrgPhoneLineRequest.
 * Use `create(DeleteOrgPhoneLineRequestSchema)` to create a new message.
 */
export const DeleteOrgPhoneLineRequestSchema: GenMessage<DeleteOrgPhoneLineRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 55);

/**
 * @generated from message hero.orgs.v1.DeleteOrgPhoneLineResponse
 */
export type DeleteOrgPhoneLineResponse = Message<"hero.orgs.v1.DeleteOrgPhoneLineResponse"> & {
};

/**
 * Describes the message hero.orgs.v1.DeleteOrgPhoneLineResponse.
 * Use `create(DeleteOrgPhoneLineResponseSchema)` to create a new message.
 */
export const DeleteOrgPhoneLineResponseSchema: GenMessage<DeleteOrgPhoneLineResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 56);

/**
 * @generated from message hero.orgs.v1.SetPrimaryOrgPhoneLineRequest
 */
export type SetPrimaryOrgPhoneLineRequest = Message<"hero.orgs.v1.SetPrimaryOrgPhoneLineRequest"> & {
  /**
   * @generated from field: int32 org_id = 1;
   */
  orgId: number;

  /**
   * @generated from field: int32 phone_line_id = 2;
   */
  phoneLineId: number;
};

/**
 * Describes the message hero.orgs.v1.SetPrimaryOrgPhoneLineRequest.
 * Use `create(SetPrimaryOrgPhoneLineRequestSchema)` to create a new message.
 */
export const SetPrimaryOrgPhoneLineRequestSchema: GenMessage<SetPrimaryOrgPhoneLineRequest> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 57);

/**
 * @generated from message hero.orgs.v1.SetPrimaryOrgPhoneLineResponse
 */
export type SetPrimaryOrgPhoneLineResponse = Message<"hero.orgs.v1.SetPrimaryOrgPhoneLineResponse"> & {
  /**
   * @generated from field: hero.orgs.v1.OrgPhoneLine org_phone_line = 1;
   */
  orgPhoneLine?: OrgPhoneLine;
};

/**
 * Describes the message hero.orgs.v1.SetPrimaryOrgPhoneLineResponse.
 * Use `create(SetPrimaryOrgPhoneLineResponseSchema)` to create a new message.
 */
export const SetPrimaryOrgPhoneLineResponseSchema: GenMessage<SetPrimaryOrgPhoneLineResponse> = /*@__PURE__*/
  messageDesc(file_hero_orgs_v1_orgs, 58);

/**
 * @generated from enum hero.orgs.v1.ServiceType
 */
export enum ServiceType {
  /**
   * @generated from enum value: SERVICE_TYPE_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * @generated from enum value: SERVICE_TYPE_DEMO = 1;
   */
  DEMO = 1,

  /**
   * @generated from enum value: SERVICE_TYPE_PRODUCTION = 2;
   */
  PRODUCTION = 2,
}

/**
 * Describes the enum hero.orgs.v1.ServiceType.
 */
export const ServiceTypeSchema: GenEnum<ServiceType> = /*@__PURE__*/
  enumDesc(file_hero_orgs_v1_orgs, 0);

/**
 * @generated from service hero.orgs.v1.OrgsService
 */
export const OrgsService: GenService<{
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrg
   */
  createOrg: {
    methodKind: "unary";
    input: typeof CreateOrgRequestSchema;
    output: typeof CreateOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.UpdateOrg
   */
  updateOrg: {
    methodKind: "unary";
    input: typeof UpdateOrgRequestSchema;
    output: typeof UpdateOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.DeleteOrg
   */
  deleteOrg: {
    methodKind: "unary";
    input: typeof DeleteOrgRequestSchema;
    output: typeof DeleteOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetOrg
   */
  getOrg: {
    methodKind: "unary";
    input: typeof GetOrgRequestSchema;
    output: typeof GetOrgResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ListOrgs
   */
  listOrgs: {
    methodKind: "unary";
    input: typeof ListOrgsRequestSchema;
    output: typeof ListOrgsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ValidateOrgCreds
   */
  validateOrgCreds: {
    methodKind: "unary";
    input: typeof ValidateOrgCredsRequestSchema;
    output: typeof ValidateOrgCredsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrgAPIUser
   */
  createOrgAPIUser: {
    methodKind: "unary";
    input: typeof CreateOrgAPIUserRequestSchema;
    output: typeof CreateOrgAPIUserResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetOrgAPIUserPrivateById
   */
  getOrgAPIUserPrivateById: {
    methodKind: "unary";
    input: typeof GetOrgAPIUserPrivateByIdRequestSchema;
    output: typeof GetOrgAPIUserPrivateByIdResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetZelloChannels
   */
  getZelloChannels: {
    methodKind: "unary";
    input: typeof GetZelloChannelsRequestSchema;
    output: typeof GetZelloChannelsResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.InsertOrgQueue
   */
  insertOrgQueue: {
    methodKind: "unary";
    input: typeof InsertOrgQueueRequestSchema;
    output: typeof InsertOrgQueueResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrgTwilioQueue
   */
  createOrgTwilioQueue: {
    methodKind: "unary";
    input: typeof CreateOrgTwilioQueueRequestSchema;
    output: typeof CreateOrgTwilioQueueResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.CreateCognitoUser
   */
  createCognitoUser: {
    methodKind: "unary";
    input: typeof CreateCognitoUserRequestSchema;
    output: typeof CreateCognitoUserResponseSchema;
  },
  /**
   * Organization Contact Book Management
   *
   * @generated from rpc hero.orgs.v1.OrgsService.AddToContactBook
   */
  addToContactBook: {
    methodKind: "unary";
    input: typeof AddToContactBookRequestSchema;
    output: typeof AddToContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.UpdateContactInContactBook
   */
  updateContactInContactBook: {
    methodKind: "unary";
    input: typeof UpdateContactInContactBookRequestSchema;
    output: typeof UpdateContactInContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.DeleteFromContactBook
   */
  deleteFromContactBook: {
    methodKind: "unary";
    input: typeof DeleteFromContactBookRequestSchema;
    output: typeof DeleteFromContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetContactFromContactBook
   */
  getContactFromContactBook: {
    methodKind: "unary";
    input: typeof GetContactFromContactBookRequestSchema;
    output: typeof GetContactFromContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ListContactsInContactBook
   */
  listContactsInContactBook: {
    methodKind: "unary";
    input: typeof ListContactsInContactBookRequestSchema;
    output: typeof ListContactsInContactBookResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetContactByPhoneNumber
   */
  getContactByPhoneNumber: {
    methodKind: "unary";
    input: typeof GetContactByPhoneNumberRequestSchema;
    output: typeof GetContactByPhoneNumberResponseSchema;
  },
  /**
   * Phone Line Management
   *
   * @generated from rpc hero.orgs.v1.OrgsService.CreateOrgPhoneLine
   */
  createOrgPhoneLine: {
    methodKind: "unary";
    input: typeof CreateOrgPhoneLineRequestSchema;
    output: typeof CreateOrgPhoneLineResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.GetOrgPhoneLineByTwilioNumber
   */
  getOrgPhoneLineByTwilioNumber: {
    methodKind: "unary";
    input: typeof GetOrgPhoneLineByTwilioNumberRequestSchema;
    output: typeof GetOrgPhoneLineByTwilioNumberResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.ListOrgPhoneLines
   */
  listOrgPhoneLines: {
    methodKind: "unary";
    input: typeof ListOrgPhoneLinesRequestSchema;
    output: typeof ListOrgPhoneLinesResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.UpdateOrgPhoneLine
   */
  updateOrgPhoneLine: {
    methodKind: "unary";
    input: typeof UpdateOrgPhoneLineRequestSchema;
    output: typeof UpdateOrgPhoneLineResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.DeleteOrgPhoneLine
   */
  deleteOrgPhoneLine: {
    methodKind: "unary";
    input: typeof DeleteOrgPhoneLineRequestSchema;
    output: typeof DeleteOrgPhoneLineResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.SetPrimaryOrgPhoneLine
   */
  setPrimaryOrgPhoneLine: {
    methodKind: "unary";
    input: typeof SetPrimaryOrgPhoneLineRequestSchema;
    output: typeof SetPrimaryOrgPhoneLineResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.TurnOnGuestMode
   */
  turnOnGuestMode: {
    methodKind: "unary";
    input: typeof TurnOnGuestModeRequestSchema;
    output: typeof TurnOnGuestModeResponseSchema;
  },
  /**
   * @generated from rpc hero.orgs.v1.OrgsService.TurnOffGuestMode
   */
  turnOffGuestMode: {
    methodKind: "unary";
    input: typeof TurnOffGuestModeRequestSchema;
    output: typeof TurnOffGuestModeResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_orgs_v1_orgs, 0);

