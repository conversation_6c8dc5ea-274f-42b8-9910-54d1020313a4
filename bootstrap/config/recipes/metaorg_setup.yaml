# This file bootstraps the meta org with the superadmin role
# and assigns it <NAME_EMAIL> user

# Whereas in general each org is isolated,
# The meta org has super-permissions across orgs
# This is  necessary for bootstrapping new orgs as well
# as powering some cross-org functionality like signup and authentication lambdas

# note that these steps must be run with the server in 
# insecure mode, since otherwise without any org or roles,
# there is no user with any creds to do anything

# this meta org should always have org id of "-1"
steps:
  - name: create_org
    description: Create the initial organization
    rpc: hero.orgs.v1.OrgsService/CreateOrg
    payload:
      org:
        name: "Meta Org"
        id: "-1"
        service_type: SERVICE_TYPE_DEMO
        domains: ["example.com"]
        primary_phone_number: "+12345678900"
        twilio_number: "+10000000000"
        twiml_app_sid: "AP367659bb9684ba033a80685ee537ffcc"
        template_id: "local"

  - name: create_admin_role
    description: Create the admin role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "superadmin"
        org_id: "-1"
        categories:
          - name: "Permission"
            can_do_all: true
          - name: "Orgs"
            can_do_all: true
          - name: "AssetRegistry"
            can_do_all: true
          - name: "Situation"
            can_do_all: true
          - name: "Report"
            can_do_all: true
          - name: "Camera"
            can_do_all: true
          - name: "CameraFeed"
            can_do_all: true
          - name: "CameraOrchestration"
            can_do_all: true
          - name: "Order"
            can_do_all: true
          - name: "Entity"
            can_do_all: true
          - name: "VideoCall"
            can_do_all: true
          - name: "Chat"
            can_do_all: true
          - name: "CellularCall"
            can_do_all: true
          - name: "PTT"
            can_do_all: true
          - name: "Case"
            can_do_all: true
          - name: "FileRepository"
            can_do_all: true
    depends_on:
      - create_org

  - name: assign_admin_role
    description: Assign the admin role to the default user
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_admin_role.role.id}"
      user_id: "cognito:88313340-d031-70f1-34bb-43128dace845" # <EMAIL>
      org_id_override: "-1"
    depends_on:
      - create_admin_role

# after completing the above steps, the following steps can be run with or without insecure mode 
  - name: create_basic_auth_role
    description: Create the basic auth role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "basic_auth_middleware"
        org_id: "-1"
        categories:
          - name: "Orgs"
            can_do_all: true
    depends_on:
      - create_org

  - name: create_basic_auth_role_assignment
    description: Create the basic auth role assignment
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_basic_auth_role.role.id}"
      user_id: "bot_basic-auth-lambda"
      org_id_override: "-1"
    depends_on:
      - create_basic_auth_role

  - name: create_post_confirmation_role
    description: Create the post confirmation role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "post_confirmation_lambda"
        org_id: "-1"
        categories:
          - name: "Orgs"
            can_do_all: true
          - name: "Permission"
            actions:
              - name: "AddCognitoUserToRole"
                can_do_action: true
          - name: "AssetRegistry"
            actions:
              - name: "CreateResponderAsset"
                can_do_action: true
    depends_on:
      - create_org

  - name: create_post_confirmation_role_assignment
    description: Create the post confirmation role assignment
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_post_confirmation_role.role.id}"
      user_id: "bot_post-confirmation-lambda"
      org_id_override: "-1"
    depends_on:
      - create_post_confirmation_role

  - name: create_pre_signup_role
    description: Create the pre signup role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "pre_signup_lambda"
        org_id: "-1"
        categories:
          - name: "Orgs"
            can_do_all: true
    depends_on:
      - create_org

  - name: create_pre_signup_role_assignment
    description: Create the pre signup role
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_pre_signup_role.role.id}"
      user_id: "bot_pre-signup-lambda"
      org_id_override: "-1"
    depends_on:
      - create_pre_signup_role

  - name: create_camera_listener_role
    description: Create the camera listener role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "camera_listener_lambda"
        org_id: "-1"
        categories:
          - name: "Orgs"
            can_do_all: true

  - name: create_camera_listener_role_assignment
    description: Create the camera listener role assignment
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_camera_listener_role.role.id}"
      user_id: "bot_camera-listener-lambda"
      org_id_override: "-1"
    depends_on:
      - create_camera_listener_role
