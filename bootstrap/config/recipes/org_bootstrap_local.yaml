steps:
  - name: create_org
    description: Create the initial organization
    rpc: hero.orgs.v1.OrgsService/CreateOrg
    payload:
      org:
        name: "Default Org"
        service_type: SERVICE_TYPE_DEMO
        domains: ["example.com"]
        primary_phone_number: "+12345678900"
        template_id: "local"

  - name: create_primary_phone_line
    description: Create the primary phone line for the organization
    rpc: hero.orgs.v1.OrgsService/CreateOrgPhoneLine
    payload:
      org_id: "${create_org.org.id}"
      line_type: "general"
      twilio_receiving_number: "+18556976298"
      twilio_number_sid: "PN1234567890abcdef1234567890abcdef"
      twiml_app_sid: "AP367659bb9684ba033a80685ee537ffcc"
      is_primary_line: true
    depends_on:
      - create_org

  - name: insert_org_queue
    description: Insert the initial twilio queue for local testing
    rpc: hero.orgs.v1.OrgsService/InsertOrgQueue
    payload:
      friendly_name: "hero-default-queue"
      twilio_queue_sid: "QUc5b1f3f717c845efed0d0fd98107552e"
      description: "Default Twilio queue for local testing cellular"
      org_id: "${create_org.org.id}"
    depends_on:
      - create_org

  - name: create_admin_role
    description: Create the admin role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "Admin"
        org_id: "${create_org.org.id}"
        categories:
          - name: "Permission"
            can_do_all: true
          - name: "Orgs"
            can_do_all: true
          - name: "AssetRegistry"
            can_do_all: true
          - name: "Situation"
            can_do_all: true
          - name: "Report"
            can_do_all: true
          - name: "Camera"
            can_do_all: true
          - name: "CameraFeed"
            can_do_all: true
          - name: "CameraOrchestration"
            can_do_all: true
          - name: "Order"
            can_do_all: true
          - name: "Entity"
            can_do_all: true
          - name: "VideoCall"
            can_do_all: true
          - name: "Chat"
            can_do_all: true
          - name: "CellularCall"
            can_do_all: true
          - name: "PTT"
            can_do_all: true
          - name: "Case"
            can_do_all: true
          - name: "FileRepository"
            can_do_all: true
    depends_on:
      - create_org

  - name: create_asset
    description: Create your asset
    rpc: hero.assets.v2.AssetRegistryService/CreateAsset
    payload:
      asset:
        name: "Default Asset"
        org_id: "${create_org.org.id}"
        type: ASSET_TYPE_DISPATCHER
        status: ASSET_STATUS_AVAILABLE
        contact_no: "+12345678900"
        contact_email: "<EMAIL>"
        resource_type: "ASSET"
        cognito_jwt_sub: "${auth.token.sub}"
    depends_on:
      - create_org

  - name: assign_admin_role
    description: Assign the admin role to the default user
    rpc: hero.permissions.v1.PermissionService/AddAssetToRole
    payload:
      role_id: "${create_admin_role.role.id}"
      asset_id: "${create_asset.asset.id}"
      org_id_override: "${create_org.org.id}"
    depends_on:
      - create_asset
      - create_admin_role

  - name: create_webhook_role
    description: Create the webhook role
    rpc: hero.permissions.v1.PermissionService/CreateRole
    payload:
      role:
        name: "webhook"
        org_id: "${create_org.org.id}"
        categories:
          - name: "AssetRegistry"
            can_do_all: true
          - name: "Situation"
            can_do_all: true
          - name: "CellularCall"
            can_do_all: true
          - name: "TwilioWebhook"
            can_do_all: true
          - name: "Orgs"
            actions:
              - name: "GetOrgAPIUserPrivateById"
                can_do_action: true

  - name: create_org_api_user
    description: Create the org api user, for the webhook to use
    rpc: hero.orgs.v1.OrgsService/CreateOrgAPIUser
    payload:
      org_id: "${create_org.org.id}"
    depends_on:
      - create_org

  - name: create_webhook_role_assignment
    description: Create the webhook role assignment
    rpc: hero.permissions.v1.PermissionService/AddUserToRole
    payload:
      role_id: "${create_webhook_role.role.id}"
      user_id: "${create_org_api_user.username}"
      org_id_override: "${create_org.org.id}"
    depends_on:
      - create_webhook_role