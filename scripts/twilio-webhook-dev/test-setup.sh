#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing webhook development setup...${NC}"

# Function to check command exists
check_command() {
    if command -v "$1" &> /dev/null; then
        echo -e "${GREEN}✅ $1 is installed${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 is not installed${NC}"
        return 1
    fi
}

# Function to check Docker service
check_docker_service() {
    local service="$1"
    if docker-compose ps "$service" | grep -q "Up"; then
        echo -e "${GREEN}✅ $service is running${NC}"
        return 0
    else
        echo -e "${RED}❌ $service is not running${NC}"
        return 1
    fi
}

# Function to get database value
get_db_value() {
    local query="$1"
    docker-compose exec -T postgres psql -U postgres -d mydatabase -t -A -c "$query" 2>/dev/null | tr -d ' ' || echo ""
}

echo -e "\n${YELLOW}📋 Checking prerequisites...${NC}"

# Check required commands
all_good=true
check_command "ngrok" || all_good=false
check_command "docker-compose" || all_good=false
check_command "curl" || all_good=false
check_command "jq" || { echo -e "${YELLOW}⚠️  jq not installed (optional, for JSON parsing)${NC}"; }

# Check Docker services
echo -e "\n${YELLOW}🐳 Checking Docker services...${NC}"
check_docker_service "postgres" || all_good=false
check_docker_service "communications-service" || all_good=false
check_docker_service "orgs-service" || all_good=false

# Check database connection and org 1
echo -e "\n${YELLOW}🗄️  Checking database...${NC}"
org_data=$(get_db_value "SELECT o.id, o.name, opl.twiml_app_sid FROM orgs o LEFT JOIN org_phone_lines opl ON o.id = opl.org_id AND opl.is_primary_line = true WHERE o.id = 1;")
if [ -n "$org_data" ]; then
    IFS='|' read -r org_id org_name app_sid <<< "$org_data"
    echo -e "${GREEN}✅ Organization 1 found: $org_name${NC}"
    
    if [ -n "$app_sid" ]; then
        echo -e "${GREEN}✅ Twilio app configured: $app_sid${NC}"
    else
        echo -e "${RED}❌ No Twilio app configured for org 1${NC}"
        all_good=false
    fi
    
    # Check API credentials
    creds=$(get_db_value "SELECT username FROM org_api_users WHERE org_id = 1 LIMIT 1;")
    if [ -n "$creds" ]; then
        echo -e "${GREEN}✅ API credentials found for org 1${NC}"
    else
        echo -e "${RED}❌ No API credentials found for org 1${NC}"
        all_good=false
    fi
else
    echo -e "${RED}❌ Organization 1 not found in database${NC}"
    echo -e "${YELLOW}💡 Create an organization first: make run && visit admin interface${NC}"
    all_good=false
fi

# Check Twilio credentials
echo -e "\n${YELLOW}📞 Checking Twilio credentials...${NC}"
twilio_sid=$(docker-compose exec -T communications-service printenv TWILIO_ACCOUNT_SID 2>/dev/null || echo "")
if [ -n "$twilio_sid" ]; then
    echo -e "${GREEN}✅ Twilio Account SID found: ${twilio_sid:0:20}...${NC}"
else
    echo -e "${RED}❌ Twilio Account SID not found${NC}"
    all_good=false
fi

twilio_token=$(docker-compose exec -T communications-service printenv TWILIO_AUTH_TOKEN 2>/dev/null || echo "")
if [ -n "$twilio_token" ]; then
    echo -e "${GREEN}✅ Twilio Auth Token found${NC}"
else
    echo -e "${RED}❌ Twilio Auth Token not found${NC}"
    all_good=false
fi

# Test webhook endpoint
echo -e "\n${YELLOW}🔗 Testing webhook endpoint...${NC}"
if curl -s --max-time 5 http://localhost:9084/health > /dev/null; then
    echo -e "${GREEN}✅ Communications service is accessible${NC}"
else
    echo -e "${RED}❌ Communications service not accessible on localhost:9084${NC}"
    all_good=false
fi

# Final verdict
echo -e "\n${BLUE}═══════════════════════════════════════${NC}"
if [ "$all_good" = true ]; then
    echo -e "${GREEN}🎉 All checks passed! Ready for webhook development.${NC}"
    echo -e "${YELLOW}💡 Run 'make dev-webhooks' to start webhook development${NC}"
    exit 0
else
    echo -e "${RED}❌ Some checks failed. Please fix the issues above.${NC}"
    echo -e "${YELLOW}💡 Make sure services are running: make run${NC}"
    exit 1
fi